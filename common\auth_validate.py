from fastapi import HTT<PERSON>Exception, status, Request
from starlette.responses import JSONResponse
import jwt
import time
from starlette.middleware.base import BaseHTTPMiddleware
from config import Config
from fastapi.openapi.utils import get_openapi


# JWT Configuration from environment variables
SECRET_KEY = Config.JWT_SECRET_KEY
ALGORITHM = Config.JWT_ALGORITHM
JWT_ISSUER = Config.JWT_ISSUER
JWT_AUDIENCE = Config.JWT_AUDIENCE
PUBLIC_ROUTES = ['/ai/docs', '/ai/openapi.json', '/ai/redoc']

def verify_jwt_token(token: str):
    """
    Verifies the validity of a JWT token by decoding it using a secret key and specified algorithm.

    - The function attempts to decode the provided token. If the token is expired or invalid, 
    it raises an HTTPException with a 401 Unauthorized status and an appropriate error message.
    - If the token is valid, it returns the decoded payload.

    Args:
        - token (str): The JWT token to be verified.

    Returns:
        - dict: The decoded payload of the JWT token if valid.

    Raises:
        - HTTPException: If the token is expired or invalid, with a 401 Unauthorized status code 
        and an appropriate error message.
    """

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Enhanced JWT validation with issuer and audience verification
        decode_options = {"verify_aud": bool(JWT_AUDIENCE)}

        payload = jwt.decode(
            token,
            SECRET_KEY,
            algorithms=[ALGORITHM],
            issuer=JWT_ISSUER,
            audience=JWT_AUDIENCE if JWT_AUDIENCE else None,
            options=decode_options
        )

        # Validate required claims based on your token structure
        required_claims = ['nameid', 'unique_name', 'role']
        for claim in required_claims:
            if claim not in payload:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail=f"Missing required claim: {claim}",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        # Check token expiration and add warning if close to expiry
        exp_timestamp = payload.get('exp')
        if exp_timestamp:
            time_until_expiry = exp_timestamp - time.time()
            if time_until_expiry < 0:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Token has expired",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        return payload

    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token signature",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidIssuerError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token issuer",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidAudienceError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token audience",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError:
        raise credentials_exception
    
class JWTMiddleware(BaseHTTPMiddleware):
    """
    JWTMiddleware is a custom middleware that verifies the presence and validity of a JWT token in the 
    Authorization header of incoming HTTP requests, except for routes listed in PUBLIC_ROUTES.

    - If the request URL path is not in PUBLIC_ROUTES, the middleware checks for the presence of a 
    valid Authorization header containing a JWT token.
    - If the token is missing or invalid, an HTTP 401 Unauthorized response is raised.
    - If the token is valid, the request is passed on to the next middleware or route handler.

    Attributes:
        - PUBLIC_ROUTES (list): A list of paths that do not require authentication.

    Methods:
        - dispatch(request: Request, call_next): Handles the request and token verification logic. 
        If the token is valid, the request is passed on to the next middleware or route handler.
    """
    
    async def dispatch(self, request: Request, call_next):
        if request.url.path not in PUBLIC_ROUTES:
            token = request.headers.get("Authorization")
            if token:
                token = token.split(" ")[1] if token.startswith("Bearer ") else token
                try:
                    payload = verify_jwt_token(token)
                    # Add user info to request state
                    request.state.user = payload
                except HTTPException as e:
                    return JSONResponse(
                        status_code=e.status_code,
                        content={"detail": e.detail},
                        headers=e.headers
                    )
            else:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Missing or invalid token"},
                    headers={"WWW-Authenticate": "Bearer"}
                )

        response = await call_next(request)
        return response


def setup_swagger_auth(app):
    """
    Setup Swagger UI authentication by configuring OpenAPI security scheme.
    This function adds JWT Bearer authentication to Swagger UI interface.

    Args:
        app: FastAPI application instance
    """
    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        
        openapi_schema = get_openapi(
            title=app.title,
            version="1.0.0",
            description=app.description,
            routes=app.routes,
        )

        # Add security scheme for JWT Bearer authentication
        openapi_schema["components"]["securitySchemes"] = {
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "Enter your JWT token"
            }
        }

        # Apply security globally to all endpoints except public routes
        for path_item in openapi_schema["paths"].values():
            for operation in path_item.values():
                if isinstance(operation, dict) and "operationId" in operation:
                    # Check if this is a public route
                    operation_path = None
                    for path, path_item_check in openapi_schema["paths"].items():
                        if path_item_check == path_item:
                            operation_path = path
                            break

                    # Don't add security to public routes
                    if operation_path not in PUBLIC_ROUTES:
                        operation["security"] = [{"BearerAuth": []}]

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    # Apply the custom OpenAPI schema to the app
    app.openapi = custom_openapi