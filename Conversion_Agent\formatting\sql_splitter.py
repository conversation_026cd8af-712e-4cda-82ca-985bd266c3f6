import re
from typing import List


def merge_postgresql_end_patterns(statements: List[str]) -> List[str]:
    """
    Merge target database 'end;' patterns with following dollar-quoted strings for proper statement formation.

    This function handles target database-specific syntax where stored procedures and functions
    end with patterns like 'end;' followed by dollar-quoted strings. These patterns need
    to be merged into single statements for proper SQL parsing and execution.

    Supported Patterns:
        - end;$BODY$;
        - end objectname;$$;
        - end objectname;$customname$;
        - END;$BODY$; (case insensitive)

    Pattern Handling:
        - Patterns can be on the same line or different lines
        - Empty lines between patterns are handled gracefully
        - Maintains proper spacing and formatting

    Args:
        statements (List[str]): List of SQL statements to process and merge

    Returns:
        List[str]: List of merged SQL statements with proper PostgreSQL syntax

    Example:
        >>> statements = ["end;", "$BODY$;"]
        >>> merged = merge_postgresql_end_patterns(statements)
        >>> print(merged)  # ["end;$BODY$;"]
    """
    if not statements:
        return statements

    merged = []
    i = 0

    while i < len(statements):
        current_stmt = statements[i].strip()

        # Check if current statement ends with 'end;' pattern (with optional object name)
        if is_postgresql_end_pattern(current_stmt):
            # Look ahead for dollar-quoted string in subsequent statements
            merged_stmt = current_stmt
            j = i + 1

            # Look through subsequent statements (including empty ones)
            while j < len(statements):
                next_stmt = statements[j].strip()

                # If we find a dollar-quoted pattern, merge it
                if is_dollar_quoted_pattern(next_stmt):
                    # Merge with appropriate spacing
                    if current_stmt.endswith(';') and next_stmt.startswith('$'):
                        merged_stmt = merged_stmt + next_stmt  # Direct concatenation
                    else:
                        merged_stmt = merged_stmt + '\n' + next_stmt

                    # Skip the merged statement
                    i = j + 1
                    break
                elif next_stmt == "":
                    # Skip empty statements and continue looking
                    j += 1
                else:
                    # Found non-empty, non-dollar-quoted statement - stop looking
                    i += 1
                    break
            else:
                # Reached end of statements without finding dollar-quoted pattern
                i += 1

            merged.append(merged_stmt)
        else:
            # Regular statement - add as-is
            merged.append(current_stmt)
            i += 1

    return merged


def is_postgresql_end_pattern(statement: str) -> bool:
    """
    Check if statement matches PostgreSQL 'end;' pattern for stored procedures and functions.

    This function identifies PostgreSQL statements that end stored procedures or functions
    with the 'end;' keyword, optionally followed by an object name. These patterns are
    commonly used in PostgreSQL stored procedure definitions.

    Supported Patterns:
        - end;
        - end objectname;
        - END; (case insensitive)
        - END ObjectName; (case insensitive)
        - end   ; (with whitespace variations)

    Args:
        statement (str): SQL statement to check for PostgreSQL end pattern

    Returns:
        bool: True if statement matches PostgreSQL end pattern, False otherwise

    Example:
        >>> is_postgresql_end_pattern("end;")
        True
        >>> is_postgresql_end_pattern("END procedure_name;")
        True
        >>> is_postgresql_end_pattern("SELECT * FROM table;")
        False
    """
    if not statement:
        return False

    # Remove extra whitespace and normalize for pattern matching
    stmt = statement.strip()

    # Pattern: end [optional_name] [optional_whitespace] ;
    pattern = r'\bend\s*(?:\w+\s*)?\s*;$'
    return bool(re.search(pattern, stmt, re.IGNORECASE))


def is_dollar_quoted_pattern(statement: str) -> bool:
    """
    Check if statement is a PostgreSQL dollar-quoted string pattern.

    This function identifies PostgreSQL dollar-quoted strings that are used to terminate
    stored procedure and function definitions. Dollar-quoting is a PostgreSQL-specific
    feature for avoiding quote escaping in function bodies.

    Supported Patterns:
        - $BODY$;
        - $$;
        - $customname$;
        - $BODY$   ; (with whitespace variations)

    Args:
        statement (str): SQL statement to check for dollar-quoted pattern

    Returns:
        bool: True if statement matches dollar-quoted pattern, False otherwise

    Example:
        >>> is_dollar_quoted_pattern("$BODY$;")
        True
        >>> is_dollar_quoted_pattern("$$;")
        True
        >>> is_dollar_quoted_pattern("SELECT * FROM table;")
        False
    """
    if not statement:
        return False

    stmt = statement.strip()

    # Pattern: $[optional_name]$ [optional_whitespace] [optional_semicolon]
    pattern = r'^\$\w*\$\s*;?\s*$'
    return bool(re.match(pattern, stmt))


def split_sql_statements(sql_code: str) -> List[str]:
    """
    Split SQL code into individual statements while preserving logical constructs and database-specific syntax.

    This function provides intelligent SQL statement splitting that handles complex scenarios
    including comments, quotes, and database-specific constructs. It's designed to work with
    both Oracle and PostgreSQL SQL code, maintaining the integrity of stored procedures,
    functions, and other complex database objects.

    Universal Splitting Rules:
        1. Split on first occurrence of 'AS' or 'IS' keywords (using word boundaries)
        2. Split on semicolons that are not within comments or quotes
        3. Preserve multi-line logical constructs as single statements
        4. Do not split within SQL comments (/* */ or --)
        5. Do not split if semicolon is within single quotes
        6. Preserve case of the original SQL
        7. Maintain structural integrity of code blocks

    PostgreSQL-Specific Handling:
        8. Merge 'end;' patterns with following dollar-quoted strings ($BODY$, $$, etc.)
        9. Handle PostgreSQL stored procedure and function syntax properly

    Comment Handling:
        - Block comments: /* comment */
        - Line comments: -- comment
        - Comments are preserved but don't affect splitting logic

    Quote Handling:
        - Single quotes: 'string content'
        - Escaped quotes: '' (two consecutive single quotes)
        - Quotes are preserved and splitting is avoided within quoted strings

    Args:
        sql_code (str): The SQL code to split into individual statements

    Returns:
        List[str]: List of individual SQL statements with preserved structure and formatting

    Example:
        >>> sql = "CREATE PROCEDURE test AS BEGIN SELECT 1; END; $BODY$;"
        >>> statements = split_sql_statements(sql)
        >>> print(len(statements))  # 2 statements
    """
    if not sql_code or not sql_code.strip():
        return []

    statements = []

    # Function to check if position is within a comment
    def is_in_comment(pos: int, text: str) -> bool:
        if pos >= len(text):
            return False

        # More efficient implementation for checking if position is within a comment
        # Check for block comments /* */
        block_comment_pairs = []
        block_starts = [m.start() for m in re.finditer(r'/\*', text[:pos+1])]
        if block_starts:
            # Only find ends that could potentially contain our position
            block_ends = [m.start() + 2 for m in re.finditer(r'\*/', text)]

            # Pair up start and end positions
            for start in block_starts:
                # Find the first end that comes after this start
                for end in block_ends:
                    if end > start:
                        block_comment_pairs.append((start, end))
                        break

        # Check if position is within any block comment
        for start, end in block_comment_pairs:
            if start < pos < end:
                return True

        # Check for line comments --
        line_starts = [m.start() for m in re.finditer(r'--', text[:pos+1])]
        for start in line_starts:
            line_end = text.find('\n', start)
            if line_end == -1:  # Comment goes to end of string
                line_end = len(text)
            if start < pos < line_end:
                return True

        return False

    # Function to check if position is within quotes
    def is_in_quotes(pos: int, text: str) -> bool:
        # More efficient implementation using regex to find all single quotes
        # and then determining if the position is within quotes
        if pos >= len(text):
            return False

        # Find all single quotes before the position
        quote_positions = [m.start() for m in re.finditer(r"'", text[:pos])]

        # Handle escaped quotes (two consecutive single quotes)
        # by removing pairs of consecutive quotes
        i = 0
        while i < len(quote_positions) - 1:
            if quote_positions[i+1] - quote_positions[i] == 1:  # Consecutive quotes
                quote_positions.pop(i)
                quote_positions.pop(i)
                # Don't increment i since we removed two elements
            else:
                i += 1

        # If there's an odd number of quotes, we're inside quotes
        return len(quote_positions) % 2 == 1



    # Process the SQL code
    remaining_code = sql_code.strip()

    # First, try to split on AS or IS keywords with word boundaries
    as_is_pattern = r'\b(AS|IS)\b'

    # Process the first occurrence of AS or IS
    # Find the first AS or IS with word boundaries
    match = re.search(as_is_pattern, remaining_code, re.IGNORECASE)

    if match and not is_in_comment(match.start(), remaining_code) and not is_in_quotes(match.start(), remaining_code):
        # Split before the match (don't include AS or IS)
        first_part = remaining_code[:match.start()]
        statements.append(first_part.strip())
        # Keep the AS or IS with the second part
        remaining_code = remaining_code[match.start():].strip()

    # If we still have code to process, split by semicolons only
    if remaining_code:
        # Find all semicolons
        semicolon_positions = [m.start() for m in re.finditer(';', remaining_code)]

        if not semicolon_positions:
            # No semicolons found, add the remaining code as a single statement
            statements.append(remaining_code.strip())
        else:
            last_pos = 0
            for pos in semicolon_positions:
                # Only split if semicolon is not in a comment or quotes
                if not is_in_comment(pos, remaining_code) and not is_in_quotes(pos, remaining_code):
                    statements.append(remaining_code[last_pos:pos+1].strip())
                    last_pos = pos + 1

            # Add any remaining code after the last semicolon
            if last_pos < len(remaining_code):
                statements.append(remaining_code[last_pos:].strip())

    # Filter out empty statements
    filtered_statements = [stmt for stmt in statements if stmt.strip()]

    # PostgreSQL-specific: Merge 'end;' patterns with following dollar-quoted strings
    merged_statements = merge_postgresql_end_patterns(filtered_statements)

    return merged_statements
