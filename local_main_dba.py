"""
Local Main for DBA Agent

This script runs the DBA agent locally with hardcoded values,
similar to main_ref.py but using the existing DBA agent workflow.
"""

import sys
from typing import Any
from llm_config.config_manager import Config<PERSON>anager
from DBA_Agent.state.state import DBARequest
from DBA_Agent.utils.dba_processor import process_dba_query
from common.common import create_llm


def setup_application() -> Any:
    """
    Set up the DBA Agent application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for database
    analysis workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    database analysis tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for database analysis operations

    Returns:
        Any: Initialized LLM instance ready for database analysis workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_application()
        >>> # LLM is now ready for database analysis tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for database analysis...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for database analysis")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_dba_analysis(query: str, target_connection_id: int) -> Any:
    """
    Execute the complete database analysis workflow.

    This function orchestrates the entire AI-driven database analysis process using
    specialized database tools and AI to provide comprehensive answers about database
    health, performance, and structure.

    Workflow Steps:
        1. Process natural language query
        2. Execute appropriate database tools
        3. Analyze results using AI
        4. Format response with recommendations
        5. Provide structured output with SQL code and insights

    Args:
        query (str): Natural language query about the database

    Returns:
        Any: DBA response containing answer, details, results, SQL code, and recommendations

    Example:
        >>> result = run_dba_analysis("What is the database size?")
        >>> print(f"Answer: {result.answer}")
    """
    try:
        print(f"🔍 Processing query: {query}")
        
        # Create DBA request
        request = DBARequest(query=query, target_connection_id=target_connection_id)
        
        # Process the query using existing DBA processor
        response = process_dba_query(request)
        
        return response
        
    except Exception as e:
        print(f"❌ DBA analysis failed: {str(e)}")
        raise


def display_dba_response(response: Any) -> None:
    """
    Display the DBA response in a formatted way for terminal output.

    Args:
        response: DBA response object containing analysis results
    """
    print("\n" + "=" * 80)
    print("📊 DATABASE ANALYSIS RESULTS")
    print("=" * 80)

    if hasattr(response, 'answer') and response.answer:
        print(f"\n{response.answer}")

    if hasattr(response, 'details') and response.details:
        print(f"\n{response.details}")

    if hasattr(response, 'sql_code') and response.sql_code:
        print(f"\n💻 SQL Code:")
        print("-" * 40)
        print(response.sql_code)
        print("-" * 40)

    if hasattr(response, 'results') and response.results:
        print(f"\n{response.results}")

    if hasattr(response, 'recommendations') and response.recommendations:
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(response.recommendations, 1):
            print(f"   {i}. {rec}")

    print("\n" + "=" * 80)


def main():
    """
    Main entry point for the DBA Agent local execution.

    This function demonstrates the complete database analysis workflow using a hardcoded
    query that users can easily modify in the code. Similar to main_ref.py pattern,
    users can change the query variable below to test different database questions.

    The main function:
        - Sets up the AI application with proper LLM configuration
        - Executes the database analysis query
        - Handles errors gracefully with detailed error reporting
        - Provides clear success/failure feedback

    To use different queries, simply modify the 'query' variable below with your question.

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        print("🚀 Starting DBA Agent Local Execution...")
        print("=" * 60)

        # Hardcoded database query for testing the DBA agent workflow
        # Users can modify this query to test different database questions
        # Examples:
        # - "What is the total size of the database?"
        # - "Show me the largest tables in the database"
        # - "What are the current database configuration parameters?"
        # - "Check the database performance metrics"
        # - "List all indexes in the database"
        # - "Show me tables with the most rows"
        # - "What is the database uptime?"

        query = "What is the total size of the database and provide the query used?"
        target_connection_id = 9  # Default connection ID for local testing

        print("📊 Database Query:")
        print(f"   Query: {query}")
        print(f"   Target Connection ID: {target_connection_id}")
        print()

        # Setup the application
        setup_application()

        # Process the query
        print("🔄 Processing database query...")

        # Run the DBA analysis
        response = run_dba_analysis(query, target_connection_id)

        # Display the response
        display_dba_response(response)

        print("✅ DBA analysis completed successfully!")
        print("=" * 60)

    except ValueError as e:
        print(f"\n❌ Configuration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
