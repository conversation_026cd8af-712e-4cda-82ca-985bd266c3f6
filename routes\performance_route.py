from fastapi import APIRouter, HTTPException

# Import Performance agent components
from Performance_Agent.state.state import PerformanceRequest, PerformanceResponse, PerformanceHistoryManager
from Performance_Agent.utils.performance_processor import process_performance_query

router = APIRouter()


@router.post("/performance-agent", response_model=PerformanceResponse)
def performance_agent(request: PerformanceRequest):
    """
    Process database performance analysis queries using AI-driven Performance agent.

    This endpoint takes natural language queries about database performance, bottlenecks,
    and optimization opportunities, then uses specialized performance analysis tools and AI
    to provide comprehensive performance insights and recommendations.
    """
    try:
        return process_performance_query(request)
    except Exception as e:
        print(f"❌ Performance agent endpoint failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Performance agent workflow failed",
                "message": str(e)
            }
        )


@router.post("/clear-history")
def clear_performance_history():
    """
    Clear the Performance agent chat history.

    This endpoint clears all stored conversation history for the Performance agent.
    """
    try:
        PerformanceHistoryManager.clear_history()
        return {"message": "Chat history cleared successfully"}
    except Exception as e:
        print(f"❌ Failed to clear chat history: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to clear chat history",
                "message": str(e)
            }
        )