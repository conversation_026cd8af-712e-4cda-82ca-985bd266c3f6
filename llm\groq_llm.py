from typing import List, Any
from langchain_groq import ChatGroq
from langchain_core.language_models.chat_models import BaseChatModel
from llm_config.config_manager import ConfigManager


class GroqLLM:
    """Groq LLM implementation using LangChain."""

    def __init__(self, config_manager: ConfigManager):
        """Initialize the Groq LLM with configuration.

        Args:
            config_manager: Configuration manager containing Groq settings
        """
        self.config_manager = config_manager
        groq_config = config_manager.models_config.groq
        self.api_key = groq_config.api_key
        self.model_name = groq_config.model_name

        # Initialize the Groq client
        self.client = ChatGroq(
            api_key=self.api_key,
            model=self.model_name,
            temperature=groq_config.temperature,
            max_tokens=groq_config.max_tokens,
        )

    def bind_tools(self, tools: List[Any], **kwargs) -> BaseChatModel:
        """Bind tools to the LLM.

        Args:
            tools: List of tools to bind to the LLM
            **kwargs: Additional keyword arguments

        Returns:
            The LLM client with tools bound to it
        """
        return self.client.bind_tools(tools)

    def get_llm(self) -> BaseChatModel:
        """Get the underlying LLM client.

        Returns:
            The Groq chat model client
        """
        return self.client