"""
Local Main for Performance Agent

This script runs the Performance agent locally with hardcoded values,
similar to main_ref.py but using the existing Performance agent workflow.
"""

import sys
from typing import Any
from llm_config.config_manager import ConfigManager
from Performance_Agent.state.state import PerformanceRequest
from Performance_Agent.utils.performance_processor import process_performance_query
from common.common import create_llm


def setup_application() -> Any:
    """
    Set up the Performance Agent application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for database
    performance analysis workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    performance analysis tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for performance analysis operations

    Returns:
        Any: Initialized LLM instance ready for performance analysis workflows

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during setup
    """
    try:
        print("🔧 Setting up Performance Agent application...")
        
        # Initialize configuration manager
        config_manager = ConfigManager()
        
        # Get and validate LLM provider
        llm_provider = config_manager.get_llm_provider()
        print(f"📋 Selected LLM Provider: {llm_provider}")
        
        # Initialize the LLM
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} for performance analysis")
        
        return llm
        
    except Exception as e:
        print(f"❌ Failed to setup application: {str(e)}")
        raise


def run_performance_analysis(query: str, target_connection_id: int) -> Any:
    """
    Execute the complete database performance analysis workflow.

    This function orchestrates the entire AI-driven performance analysis process using
    specialized performance tools and AI to provide comprehensive insights about database
    performance, bottlenecks, and optimization opportunities.

    Workflow Steps:
        1. Process natural language performance query
        2. Execute appropriate performance analysis tools
        3. Analyze results using AI
        4. Format response with optimization recommendations
        5. Provide structured output with SQL code and performance insights

    Args:
        query (str): Natural language query about database performance

    Returns:
        Any: Performance response containing answer, details, results, SQL code, and recommendations

    Raises:
        ValueError: If query is invalid or empty
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        print(f"🚀 Starting performance analysis workflow...")
        print(f"📊 Performance Query: {query}")
        
        # Validate query
        if not query or not query.strip():
            raise ValueError("Performance query cannot be empty")
        
        # Create performance request
        request = PerformanceRequest(query=query.strip(), target_connection_id=target_connection_id)
        
        # Process the performance query
        print("🔍 Processing performance query...")
        response = process_performance_query(request)
        
        print("✅ Performance analysis completed successfully")
        return response
        
    except Exception as e:
        print(f"❌ Performance analysis failed: {str(e)}")
        raise


def display_performance_results(response: Any) -> None:
    """
    Display the performance analysis results in a formatted way.

    Args:
        response: The performance response object containing analysis results
    """
    print("\n" + "=" * 80)
    print("📊 PERFORMANCE ANALYSIS RESULTS")
    print("=" * 80)

    if hasattr(response, 'answer') and response.answer:
        print(f"\n{response.answer}")

    if hasattr(response, 'details') and response.details:
        print(f"\n{response.details}")

    if hasattr(response, 'results') and response.results:
        print(f"\n{response.results}")

    if hasattr(response, 'sql_code') and response.sql_code:
        print(f"\n💻 SQL Code:")
        print("-" * 40)
        print(response.sql_code)
        print("-" * 40)

    if hasattr(response, 'recommendations') and response.recommendations:
        print(f"\n💡 Recommendations:")
        for i, rec in enumerate(response.recommendations, 1):
            print(f"   {i}. {rec}")

    print("\n" + "=" * 80)


def main():
    """
    Main function to execute the Performance Agent local workflow.

    This function demonstrates the complete Performance Agent workflow by:
        1. Setting up the application and LLM
        2. Processing a hardcoded performance query
        3. Displaying the formatted results

    Users can modify the hardcoded query to test different performance questions.

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        print("🚀 Starting Performance Agent Local Execution...")
        print("=" * 60)

        # Hardcoded performance query for testing the Performance agent workflow
        # Users can modify this query to test different performance questions
        # Examples:
        # - "Show me current database performance metrics"
        # - "What is the cache hit ratio?"
        # - "How many active connections are there?"

        query = "Show me current database performance metrics"
        target_connection_id = 9  # Default connection ID for local testing

        print(f"📊 Performance Query: {query}")
        print(f"   Target Connection ID: {target_connection_id}")
        print("-" * 60)

        # Setup application
        print("🔧 Setting up Performance Agent...")
        setup_application()
        
        # Run performance analysis
        print("⚡ Running performance analysis...")
        response = run_performance_analysis(query, target_connection_id)
        
        # Display results
        display_performance_results(response)
        
        print("🎉 Performance Agent execution completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Performance Agent execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Performance Agent execution failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
