
set search_path to LAB;
create or replace function LAB.FN_GETPATIENTDETAILS(IN_UHID IN VARCHAR)
returns varchar
language plpgsql
security definer as $BODY$
declare
V_PATIENTDETAILS VARCHAR(500);
begin
set search_path to LAB;
SELECT(SELECT TLM.TITLETYPE
FROM EHIS.TITLEMASTER TLM
WHERE P.TITLE = TLM.TITLECODE)|| ' ' || P.FIRSTNAME || ' ' ||
P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
(SELECT SM.SUFFIXNAME
FROM EHIS.SUFFIXMASTER SM
WHERE P.SUFIX = SM.SUFFIXCODE)|| ' /' || LD.LOVDETAILVALUE || ' /' ||
FLOOR(public.months_between(current_timestamp(0)::timestamp, P.BIRTHDATE)/ 12)|| 'Yr' || ' ' ||
floor(mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate), 12))|| 'Mth' || ' ' ||
current_date -
p.birthdate + INTERVAL 'date_trunc('day',public.months_between(current_timestamp(0 Months'::timestamp, p.birthdate/ 12)* 12 +
mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate::date , 12))))||
'Days'
--TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')


into strict V_PATIENTDETAILS
FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
AND(P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
P.UHID = IN_UHID);
RETURN V_PATIENTDETAILS;
end;
$BODY$;
        