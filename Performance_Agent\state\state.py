"""
Performance Agent State and Models.

This module contains all the Pydantic models and state management for the Performance agent.
"""

from typing import Optional, List, Any
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, AIMessage


class ChatMessage(BaseModel):
    """Individual chat message model."""
    role: str = Field(..., description="Role of the message sender (user or assistant)")
    content: str = Field(..., description="Content of the message")


class PerformanceRequest(BaseModel):
    """Request model for Performance agent queries."""
    query: str = Field(..., description="Natural language query about database performance", min_length=1)
    target_connection_id: int = Field(..., description="Target database connection ID for dynamic database access")


class PerformanceResponse(BaseModel):
    """Response model for Performance agent."""
    answer: str = Field(description="The main answer to the user's performance question in a clear, concise format")
    details: Optional[str] = Field(default=None, description="Additional explanation or context about the performance analysis")
    results: Optional[str] = Field(default=None, description="Formatted performance metrics and analysis results in a readable format")
    sql_code: Optional[str] = Field(default=None, description="SQL code that was generated or executed for performance analysis")
    recommendations: Optional[List[str]] = Field(default=None, description="Optional performance optimization recommendations")
    formatted_display: Optional[str] = Field(default=None, description="Complete Streamlit-style formatted response for rich display")


class PerformanceHistoryManager:
    """Manages chat history with a limit of 100 recent messages in background."""

    MAX_HISTORY_SIZE = 100

    # In-memory storage for chat history
    chat_history: List[ChatMessage] = []

    @classmethod
    def add_user_message(cls, content: str) -> None:
        """Add a user message to the background chat history."""
        cls.add_message("user", content)

    @classmethod
    def add_assistant_message(cls, content: str) -> None:
        """Add an assistant message to the background chat history."""
        cls.add_message("assistant", content)

    @classmethod
    def add_message(cls, role: str, content: str) -> None:
        """Add a message to history and maintain the 100 message limit."""
        # Add new message
        new_message = ChatMessage(role=role, content=content)
        cls.chat_history.append(new_message)

        # Keep only the last 100 messages
        if len(cls.chat_history) > cls.MAX_HISTORY_SIZE:
            cls.chat_history = cls.chat_history[-cls.MAX_HISTORY_SIZE:]

    @classmethod
    def get_chat_history(cls) -> List[ChatMessage]:
        """Get the current chat history."""
        return cls.chat_history.copy()

    @classmethod
    def convert_to_langchain_format(cls) -> List[Any]:
        """Convert chat history to LangChain message format."""
        langchain_messages = []
        for message in cls.chat_history:
            if message.role == "user":
                langchain_messages.append(HumanMessage(content=message.content))
            elif message.role == "assistant":
                langchain_messages.append(AIMessage(content=message.content))
        return langchain_messages

    @classmethod
    def clear_history(cls) -> None:
        """Clear all chat history."""
        cls.chat_history = []

    @classmethod
    def get_history_size(cls) -> int:
        """Get the current size of chat history."""
        return len(cls.chat_history)
