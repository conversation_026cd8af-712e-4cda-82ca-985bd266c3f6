"""
Utility functions for dynamic database names in QMigrator AI.

This module provides helper functions to get dynamic database names from configuration
and use them throughout the application, making the system flexible for different
database migration scenarios.
"""

from config import Config


def get_database_names():
    """Get source and target database names from configuration."""
    source_db = Config.get_source_database()
    target_db = Config.get_target_database()

    if not source_db:
        raise ValueError("SOURCE_DATABASE configuration is required")
    if not target_db:
        raise ValueError("TARGET_DATABASE configuration is required")

    return source_db, target_db


def get_database_specific_terms():
    """Get database-specific terminology for prompts."""
    source_db, target_db = get_database_names()

    return {
        'source_db': source_db,
        'target_db': target_db,
        'migration_direction': f"{source_db} to {target_db}",
        'expert_title': f"{source_db} to {target_db} Database Migration Expert"
    }
