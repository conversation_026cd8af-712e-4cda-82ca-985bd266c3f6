"""
Database tools for DBA Agent.

This module contains all the database analysis tools extracted from the Streamlit DBA agent.
"""

from langchain_community.utilities import SQLDatabase
from langchain.agents import Agent<PERSON><PERSON>cutor, create_openai_tools_agent
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.tools import tool
from urllib.parse import quote_plus
from DBA_Agent.prompts.dba_prompts import DBAPrompts


class DatabaseTools:
    """Database tools for DBA agent operations."""

    def __init__(self, target_db_credentials: dict):
        """Initialize database connection and tools with dynamic credentials."""
        self.db_credentials = target_db_credentials
        self.db_url = self.build_connection_url()
        self.db = SQLDatabase.from_uri(self.db_url)
        self.tools = self.create_tools()

    def build_connection_url(self) -> str:
        """Build PostgreSQL connection URL from credentials."""
        user = quote_plus(str(self.db_credentials.get("name", "")))
        password = quote_plus(str(self.db_credentials.get("password", "")))
        host = self.db_credentials.get("host", "")
        port = self.db_credentials.get("port", 5432)
        db_name = self.db_credentials.get("db_name", "")

        return f'postgresql+psycopg2://{user}:{password}@{host}:{port}/{db_name}'

    def create_tools(self):
        """Create all database analysis tools."""
        
        @tool
        def get_database_size() -> dict:
            """Get the total size of the database.
            
            This tool returns the size of the database in megabytes.
            Use this to check database storage usage and health.
            
            Returns:
                A dictionary with the database size information
            """
            result = self.db.run("""
                SELECT
                    pg_size_pretty(pg_database_size(current_database())) as size,
                    pg_database_size(current_database()) as size_bytes
            """)
            return result

        @tool
        def get_all_object_counts() -> dict:
            """Get the counts of various objects in the database.
            
            This function returns the total counts of tables, views, materialized views,
            sequences, stored procedures, and functions in the current database.
            Useful for getting an overview of the objects in the database.
            
            Returns:
                A dictionary containing the counts of different database objects
            """
            result = self.db.run("""
                SELECT
                  (SELECT count(*) FROM information_schema.tables WHERE table_type = 'BASE TABLE'
                   AND table_schema NOT IN ('pg_catalog', 'information_schema')) AS total_tables,
                  (SELECT count(*) FROM information_schema.tables WHERE table_type = 'VIEW'
                   AND table_schema NOT IN ('pg_catalog', 'information_schema')) as total_views,
                  (SELECT COUNT(*) FROM pg_matviews) as total_mviews,
                  (SELECT COUNT(*) AS sequence_count FROM information_schema.sequences
                   WHERE sequence_schema NOT IN ('pg_catalog', 'information_schema')) as total_sequences,
                  (SELECT COUNT(*) FROM information_schema.routines WHERE routine_type = 'PROCEDURE' AND specific_schema NOT IN ('pg_catalog', 'information_schema')) AS total_procedures,
                  (SELECT COUNT(*) FROM information_schema.routines WHERE routine_type = 'FUNCTION' AND specific_schema NOT IN ('pg_catalog', 'information_schema')) AS total_functions;
            """)
            return result

        @tool
        def get_table_row_count(table_name: str) -> dict:
            """Get the number of rows in a table.
            
            This tool returns the row count for a specific table.
            Use this to check the number of records in a table.
            
            Args:
                table_name: The name of the table to check
                
            Returns:
                A dictionary with the table name and row count
            """
            result = self.db.run(f"SELECT COUNT(*) FROM {table_name}")
            return result

        @tool
        def get_large_tables() -> dict:
            """Get a list of large tables based on row count.
            
            This function identifies user tables in the database that have more than 10 million live rows.
            It can be used to monitor and optimize large tables that might impact performance.
            
            Returns:
                A dictionary containing table names and their row counts
            """
            result = self.db.run("""
                SELECT relname AS table_name, n_live_tup
                FROM pg_stat_user_tables
                WHERE n_live_tup > 10000000
                ORDER BY n_live_tup DESC;
            """)
            return result

        @tool
        def fetch_idle_sessions() -> dict:
            """Fetch information about idle database sessions.
            
            This function retrieves a list of idle sessions from the PostgreSQL pg_stat_activity view,
            excluding the current session. Useful for identifying connection leaks.
            
            Returns:
                A dictionary containing session details for each idle connection
            """
            result = self.db.run("""
                SELECT pid, usename, application_name, backend_start
                FROM pg_stat_activity
                WHERE state = 'idle'
                  AND pid <> pg_backend_pid();
            """)
            return result

        @tool
        def fetch_active_sessions() -> dict:
            """Fetch information about active database sessions.
            
            This function retrieves all currently active sessions from the PostgreSQL pg_stat_activity view.
            Active sessions are those that are currently executing queries.
            """
            result = self.db.run("""
                SELECT pid, usename, application_name, backend_start
                FROM pg_stat_activity
                WHERE state = 'active';
            """)
            return result

        @tool
        def last_vacuum() -> dict:
            """Fetch the last vacuum times for all tables in the database.
            
            This function retrieves information showing when each table was last vacuumed.
            This can help identify tables that may require maintenance.
            """
            result = self.db.run("""
                SELECT relid, schemaname, relname, last_vacuum  
                FROM pg_stat_all_tables   
                ORDER BY last_vacuum DESC;
            """)
            return result

        @tool
        def last_analyze() -> dict:
            """Fetch the last analyze times for all tables in the database.
            
            This function retrieves information showing when each table was last analyzed.
            Regular analyze operations help the query planner make informed decisions.
            """
            result = self.db.run("""
                SELECT relid, schemaname, relname, last_analyze  
                FROM pg_stat_all_tables  
                ORDER BY last_analyze DESC;
            """)
            return result

        @tool
        def users_whocan_login() -> dict:
            """Fetch a list of users who can log in to the database.
            
            This function retrieves all user names from the PostgreSQL pg_user view.
            It lists roles that have login privileges.
            
            Returns:
                A dictionary containing user names with login privileges
            """
            result = self.db.run("""
                SELECT usename
                FROM pg_user;
            """)
            return result

        @tool
        def all_tables_bloat_percentage() -> dict:
            """Fetch bloat percentage for all user tables in the database.
            
            This function calculates the percentage of dead tuples in each user table,
            giving an estimate of table bloat. Higher percentages may indicate need for vacuuming.
            """
            result = self.db.run("""
                SELECT schemaname, relname,
                       round((n_dead_tup::numeric / (n_live_tup + n_dead_tup)) * 100, 2) AS bloat_percentage  
                FROM pg_stat_user_tables  
                WHERE n_dead_tup > 0  
                ORDER BY bloat_percentage DESC;
            """)
            return result

        @tool
        def index_size() -> dict:
            """Fetch the sizes of indexes for user tables in the database.
            
            This function retrieves the size of each index, ordered by size in descending order.
            Useful for identifying large indexes that may impact storage or performance.
            """
            result = self.db.run("""
                SELECT indexrelname, relname, pg_size_pretty(pg_relation_size(indexrelid)) AS index_size  
                FROM pg_stat_user_indexes   
                ORDER BY pg_relation_size(indexrelid) DESC;
            """)
            return result

        @tool
        def dead_tuples() -> dict:
            """Fetch tables with a high number of dead tuples.
            
            This function retrieves tables that have more than 1,000 dead tuples.
            It includes live tuple counts and vacuum activity stats for maintenance diagnosis.
            """
            result = self.db.run("""
                SELECT relname, n_live_tup, n_dead_tup, vacuum_count, autovacuum_count  
                FROM pg_stat_all_tables  
                WHERE n_dead_tup > 1000 
                ORDER BY n_dead_tup DESC;
            """)
            return result

        @tool
        def server_parameters() -> dict:
            """Fetch important PostgreSQL server configuration parameters.
            
            This function retrieves configuration parameters from the pg_settings system catalog view.
            Key performance-related parameters are prioritized at the top of the result.
            """
            result = self.db.run("""
                SELECT name as parameter_name, setting as parameter_value
                FROM pg_settings
                ORDER BY 
                    name = 'shared_buffers' DESC,
                    name = 'max_replication_slots' DESC,
                    name = 'max_wal_size' DESC,
                    name = 'maintenance_work_mem' DESC,
                    name = 'max_parallel_workers' DESC,
                    name;
            """)
            return result

        @tool
        def list_installed_extensions() -> dict:
            """Fetch a list of all user installed extensions in the PostgreSQL database.
            
            This function retrieves information about all extensions installed in the database
            from the pg_extension system catalog, ordered by extension name.
            """
            result = self.db.run("SELECT * FROM pg_extension ORDER BY extname;")
            return result

        @tool
        def all_indexes_count() -> dict:
            """Fetch the total count of indexes in the database.
            
            This function counts all indexes in the database, excluding system schemas.
            Useful for understanding the number of indexes and potential optimization areas.
            """
            result = self.db.run("""
                SELECT COUNT(*) AS index_count
                FROM pg_indexes
                WHERE schemaname NOT IN ('pg_catalog', 'information_schema');
            """)
            return result

        @tool
        def get_indexes_for_table(tablename: str, schemaname: str = None) -> dict:
            """Fetch all indexes for a specific table in the specified schema.
            
            This function retrieves the names and definitions of indexes associated with
            a table in a particular schema.
            
            Args:
                tablename (str): Name of the table or 'schema.table' format.
                schemaname (str, optional): Name of the schema the table belongs to.
                
            Returns:
                dict: A dictionary containing index names and definitions.
            """
            if '.' in tablename:
                parts = tablename.split('.')
                if len(parts) != 2:
                    raise ValueError("Invalid format. Use 'schema.table' or pass schema and table separately.")
                schemaname, tablename = parts

            if not schemaname:
                raise ValueError("Schema name must be provided either directly or via 'schema.table' format.")

            # Convert to lowercase to match PostgreSQL conventions
            tablename = tablename.lower()
            schemaname = schemaname.lower()

            # Safely build the SQL query
            query = f"""
                SELECT indexname, indexdef
                FROM pg_indexes
                WHERE tablename = '{tablename}' AND schemaname = '{schemaname}';
            """

            result = self.db.run(query)
            return result

        @tool
        def unused_indexes() -> dict:
            """Fetch the count of unused indexes in the database.
            
            This function counts indexes that have not been used since the last statistics reset.
            Excludes indexes in system schemas and primary key indexes.
            """
            result = self.db.run("""
                SELECT
               relname AS table,
               indexrelname AS index,
               pg_size_pretty(pg_relation_size(i.indexrelid)) AS index_size,
               idx_scan as index_scans
               FROM pg_stat_user_indexes ui
               JOIN pg_index i ON ui.indexrelid = i.indexrelid
               WHERE NOT indisunique AND idx_scan =0 AND pg_relation_size(relid) > 5 * 8192
               ORDER BY pg_relation_size(i.indexrelid) / nullif(idx_scan, 0) DESC NULLS FIRST,
               pg_relation_size(i.indexrelid) DESC;
            """)
            return result

        @tool
        def long_running_queries(threshold_minutes: int) -> list:
            """Fetch queries running longer than a given threshold.
            
            This function retrieves queries currently running in the PostgreSQL database
            that have been executing for more than threshold_minutes minutes.
            
            Args:
                threshold_minutes (int): The minimum duration a query must be running to be included.
                
            Returns:
                list: A list of dictionaries with details about each long-running query.
            """
            result = self.db.run(f"""
                select current_timestamp-query_start as runtime,
                query_start,pid,State,datname,usename, query FROM pg_stat_activity
                where current_timestamp-query_start> interval '{threshold_minutes} minutes' order by 1 desc;
            """)
            return result

        # Return all tools
        return [
            get_database_size, get_all_object_counts, get_table_row_count, get_large_tables,
            fetch_idle_sessions, fetch_active_sessions, last_vacuum, last_analyze,
            users_whocan_login, all_tables_bloat_percentage, index_size, dead_tuples,
            server_parameters, list_installed_extensions, all_indexes_count,
            get_indexes_for_table, unused_indexes, long_running_queries
        ]
    
    def get_tools(self):
        """Get all database tools."""
        return self.tools
    
    def create_agent_executor(self, llm) -> AgentExecutor:
        """Create an agent executor with database tools."""
        # Create a chat prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", DBAPrompts.get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])

        # Bind tools to the LLM
        llm_with_tools = llm.get_llm().bind_tools(self.tools)

        # Create an agent with the tools
        agent = create_openai_tools_agent(llm_with_tools, self.tools, prompt)

        # Create the agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=50,
            return_intermediate_steps=True
        )

        return agent_executor
