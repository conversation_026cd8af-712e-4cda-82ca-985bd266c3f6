"""
Prompts for identifying responsible features in Stage 2 conversion analysis.
"""
from typing import Dict, List, Any, Optional
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_responsible_features_identification_prompt(
    original_source: str,
    ai_converted: str,
    actual_target: str,
    deployment_error: str,
    decrypted_modules: Dict[str, str],
    keyword_mapping: List[Dict[str, Any]],
    available_features: List[tuple],
    validation_feedback: Optional[str] = None,
    db_terms: Optional[Dict[str, str]] = None
) -> str:
    """
    Create AI analysis prompt for identifying responsible modules (Stage 2 style with dynamic database names).

    Args:
        original_source: Original source database statement
        ai_converted: Expected target database statement from AI
        actual_target: Actual wrong target database statement
        deployment_error: Error message from deployment
        decrypted_modules: Dictionary of decrypted Python module contents
        keyword_mapping: Keyword-to-module mapping from CSV
        available_features: List of available feature tuples
        validation_feedback: Optional feedback from previous validation attempts
        db_terms: Database-specific terms from migration name (Request-First Approach)

    Returns:
        Formatted prompt string for AI analysis with structured output
    """

    # Use provided database terms or fallback to config-based approach
    if db_terms is None:
        db_terms = get_database_specific_terms()

    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    # Format available features for prompt
    available_features_str = "\n".join([f"- {name}: {path}" for name, path in available_features])

    # Include validation feedback if available
    feedback_section = ""
    if validation_feedback:
        feedback_section = f"""
VALIDATION FEEDBACK FROM PREVIOUS ATTEMPT:
=========================================
{validation_feedback}

Use this feedback to improve your identification accuracy. Focus on the specific guidance provided.

"""

    prompt = f"""
You are a {expert_title}. Your task is to identify which specific Python modules from the available features are responsible for {migration_direction} conversion failures.

{feedback_section}CONVERSION ANALYSIS:
===================

Original {source_db} Statement:
{original_source}

AI Corrected {target_db} Statement (Expected Output):
{ai_converted}

QMigrator {target_db} Statement (Current Output):
{actual_target}

Deployment Error:
{deployment_error}

AVAILABLE FEATURES TO ANALYZE:
=============================
{available_features_str}

DECRYPTED MODULE CODE:
=====================
"""
    
    for module_name, module_code in decrypted_modules.items():
        prompt += f"\n--- {module_name.upper()} MODULE ---\n{module_code}\n"

    prompt += f"""

KEYWORD MAPPING REFERENCE:
=========================
{keyword_mapping}

ANALYSIS TASK:
=============
1. **PRIMARY GOAL**: Identify which QMigrator modules failed to produce the AI-corrected output
2. Compare AI Corrected {target_db} vs QMigrator {target_db} to find conversion failures
3. Analyze the Deployment Error to understand specific failure points
4. Review each available feature's decrypted code to understand conversion logic
5. Focus on modules that should have handled specific {source_db} syntax but failed

IDENTIFICATION METHODOLOGY:
==========================
1. **Error Analysis**: Examine deployment error for specific syntax/keyword failures
2. **Statement Comparison**: Compare AI Corrected vs QMigrator output for differences
3. **Module Mapping**: Match {source_db} keywords to responsible conversion modules
4. **Code Review**: Analyze module code to understand conversion logic and potential failures

IDENTIFICATION CRITERIA:
=======================
**PRIMARY RULE**: Only identify modules DIRECTLY responsible for the specific deployment error.

**REQUIRED CONDITIONS** (ALL must be met):
1. Module handles EXACT keywords mentioned in deployment error
2. Module's primary purpose is converting the failing syntax element
3. Module's current code lacks logic for this specific error scenario
4. Module is responsible for the specific transformation that failed

**EXCLUSION RULES**:
- Do NOT identify modules just because they appear in the statement
- Do NOT identify modules that work correctly but happen to be present
- Do NOT identify modules whose primary purpose differs from the error cause

OUTPUT FORMAT (JSON):
====================
{{
  "responsible_features": [
    {{
      "feature_name": "<feature_name>",
      "module_path": "<relative_path>",
      "responsibility_reason": "<why this module is responsible>",
      "error_impact": "High|Medium|Low",
      "keywords_matched": ["<keyword1>", "<keyword2>"],
      "confidence_score": <float between 0.0 and 1.0>
    }}
  ],
  "analysis_summary": "<comprehensive explanation covering ALL responsible features, analysis process, comparison findings, and detailed reasoning>"
}}



VALIDATION APPROACH:
==================
For each potential module, verify:
1. Is this module's PRIMARY purpose to handle the failing syntax element?
2. Does the deployment error directly relate to this module's conversion logic?
3. Would fixing ONLY this module resolve the specific deployment error?

**Key Principle**: Be PRECISE and SPECIFIC. Quality over quantity - identify fewer, more accurate modules.

Focus ONLY on identifying responsible modules. Do NOT suggest code changes.
"""
    
    return prompt