"""
Performance tools for Performance Agent.

This module contains all the database performance analysis tools.
For now, it uses the same database tools as DBA Agent until specific performance tools are provided.
"""

from langchain_community.utilities import SQLDatabase
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_core.prompts import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MessagesPlaceholder
from langchain_core.tools import tool
from urllib.parse import quote_plus
from Performance_Agent.prompts.performance_prompts import PerformancePrompts


class PerformanceTools:
    """Performance tools for performance agent operations."""

    def __init__(self, target_db_credentials: dict):
        """Initialize database connection and tools with dynamic credentials."""
        self.db_credentials = target_db_credentials
        self.db_url = self.build_connection_url()
        self.db = SQLDatabase.from_uri(self.db_url)
        self.tools = self.create_tools()

    def build_connection_url(self) -> str:
        """Build PostgreSQL connection URL from credentials."""
        user = quote_plus(str(self.db_credentials.get("name", "")))
        password = quote_plus(str(self.db_credentials.get("password", "")))
        host = self.db_credentials.get("host", "")
        port = self.db_credentials.get("port", 5432)
        db_name = self.db_credentials.get("db_name", "")

        return f'postgresql+psycopg2://{user}:{password}@{host}:{port}/{db_name}'

    def create_tools(self):
        """Create performance analysis tools - starting with one tool."""

        @tool
        def get_performance_metrics():
            """Get current database performance metrics including connections, queries, and cache hit ratios."""
            query = """
            SELECT
                'Active Connections' as metric,
                count(*) as value
            FROM pg_stat_activity
            WHERE state = 'active'
            UNION ALL
            SELECT
                'Total Connections' as metric,
                count(*) as value
            FROM pg_stat_activity
            UNION ALL
            SELECT
                'Cache Hit Ratio' as metric,
                round(
                    (sum(blks_hit) * 100.0 / nullif(sum(blks_hit) + sum(blks_read), 0))::numeric, 2
                ) as value
            FROM pg_stat_database
            WHERE datname = current_database()
            """
            try:
                result = self.db.run(query)
                return f"Performance metrics: {result}"
            except Exception as e:
                return f"Error getting performance metrics: {str(e)}"

        @tool
        def generate_explain_plan(query: str):
            """
            Executes an EXPLAIN plan for the given SQL query using the provided DB connection object used for resolving perf issue.

            Parameters:

            - db: a database connection object with a .run(query) method
            - query (str): the SQL query to explain

            Returns:
            - str: the explain plan as a formatted string
            """
            try:
                explain_query = f"EXPLAIN {query}"
                result = self.db.run(explain_query)
                return result
            except Exception as e:
                return f"Error generating EXPLAIN plan: {e}"

        # Return both tools
        return [get_performance_metrics, generate_explain_plan]
    
    def get_tools(self):
        """Get all performance tools."""
        return self.tools

    def create_agent_executor(self, llm):
        """Create an agent executor with performance tools."""
        # Create a chat prompt template
        prompt = ChatPromptTemplate.from_messages([
            ("system", PerformancePrompts.get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history", optional=True),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])

        # Bind tools to the LLM
        llm_with_tools = llm.get_llm().bind_tools(self.tools)

        # Create an agent with the tools
        agent = create_openai_tools_agent(llm_with_tools, self.tools, prompt)

        # Create the agent executor
        agent_executor = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=50,
            return_intermediate_steps=True
        )

        return agent_executor
