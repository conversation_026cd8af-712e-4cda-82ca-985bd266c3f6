"""
DBA Query Processor.

This module contains the main processing logic for DBA agent queries.
"""

import re
from typing import Optional
from llm_config.config_manager import ConfigManager
from DBA_Agent.tools.database_tools import DatabaseTools
from DBA_Agent.prompts.dba_prompts import DBAPrompts
from DBA_Agent.state.state import DBARequest, DBAResponse, DBAHistoryManager
from common.common import create_llm


def process_dba_query(request: DBARequest) -> DBAResponse:
    """
    Process a DBA query using the agent workflow.
    
    Args:
        request: The DBA request containing the user query
        
    Returns:
        DBAResponse: The structured response from the DBA agent
    """
    try:
        print(f"🚀 Starting DBA agent workflow...")
        print(f"📊 Query: {request.query}")

        # Setup database credentials using API
        from common.api import api_authentication, decrypt_database_details

        # Get target connection details from the API
        token_data = api_authentication(1)  # Default project ID for DBA operations
        target_db_credentials = decrypt_database_details(token_data, 1, 'Target', str(request.target_connection_id))
        print(f"🔧 Using database connection ID: {request.target_connection_id}")
        print(f"✅ Target DB credentials fetched from API")

        # Setup the LLM
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider}")

        # Initialize database tools with dynamic credentials and prompts
        db_tools = DatabaseTools(target_db_credentials)
        dba_prompts = DBAPrompts()

        # Add user query to background chat history
        DBAHistoryManager.add_user_message(request.query)

        # Create agent executor with database tools
        agent_executor = db_tools.create_agent_executor(llm)

        # Get chat history from background manager
        langchain_history = DBAHistoryManager.convert_to_langchain_format()

        # Execute the query with chat history (excluding the current user message to avoid duplication)
        raw_response = agent_executor.invoke({
            "input": request.query,
            "chat_history": langchain_history[:-1] if langchain_history else []
        })
        
        # Use structured LLM to format the response
        structured_prompt = dba_prompts.get_structured_response_prompt(request.query, raw_response["output"])

        # Get structured response
        structured_llm = llm.get_llm().with_structured_output(DBAResponse)
        structured_response = structured_llm.invoke(structured_prompt)

        # Extract SQL code if not provided directly
        if not structured_response.sql_code:
            structured_response.sql_code = extract_sql_code(raw_response["output"], structured_response.results)

        # Enhance the response formatting
        enhanced_response = enhance_response_formatting(structured_response)

        # Add assistant response to background chat history
        assistant_content = f"Answer: {enhanced_response.answer}"
        if enhanced_response.details:
            assistant_content += f"\nDetails: {enhanced_response.details}"
        DBAHistoryManager.add_assistant_message(assistant_content)

        print("🎉 DBA agent workflow completed!")

        return enhanced_response

    except Exception as e:
        print(f"❌ DBA agent workflow failed: {str(e)}")
        # Return error response in the expected format
        return DBAResponse(
            answer=f"Error processing query: {str(e)}",
            details="An error occurred while processing your database query. Please check your query and try again.",
            results=None,
            sql_code=None,
            recommendations=["Please verify your query syntax", "Check database connectivity", "Try a simpler query"]
        )


def extract_sql_code(raw_response: str, results: Optional[str] = None) -> Optional[str]:
    """Extract SQL code from response text."""
    # First check in results
    if results:
        sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', results)
        if sql_code_match:
            return sql_code_match.group(1).strip()

        # Try to find any code block
        code_block_match = re.search(r'```\w*\s*([\s\S]*?)\s*```', results)
        if code_block_match:
            return code_block_match.group(1).strip()

    # Check in raw response
    sql_code_match = re.search(r'```sql\s*([\s\S]*?)\s*```', raw_response)
    if sql_code_match:
        return sql_code_match.group(1).strip()

    return None


def enhance_response_formatting(response: DBAResponse) -> DBAResponse:
    """Enhance response formatting with consistent structure for both individual fields and formatted_display."""

    # Apply consistent formatting to individual fields
    response = format_individual_fields(response)

    # Create formatted display with same structure
    formatted_display = create_formatted_display(response)
    response.formatted_display = formatted_display

    return response


def format_individual_fields(response: DBAResponse) -> DBAResponse:
    """Apply consistent formatting to individual response fields."""

    # Keep individual fields clean without headers - headers will be added in formatted_display only
    # This prevents duplicate headers from appearing

    # Clean answer field (remove any existing headers)
    if response.answer:
        response.answer = clean_content(response.answer)

    # Clean details field (remove any existing headers)
    if response.details:
        enhanced_details = clean_content(response.details)
        response.details = enhanced_details

    # Clean and enhance results field
    if response.results:
        enhanced_results = clean_content(response.results)

        # Add contextual notes for different types of results
        if "| Parameter" in response.results or "| parameter_name" in response.results:
            enhanced_results += "\n\n*Note: These are key PostgreSQL configuration parameters that affect database performance and behavior.*"
        elif "| table_name" in response.results or "| relname" in response.results:
            enhanced_results += "\n\n*Note: Table statistics help identify performance bottlenecks and maintenance needs.*"
        elif "| index" in response.results or "| indexname" in response.results:
            enhanced_results += "\n\n*Note: Index information is crucial for query optimization and storage management.*"

        response.results = enhanced_results

    # Clean sql_code field
    if response.sql_code:
        response.sql_code = clean_content(response.sql_code)

    # Clean recommendations field
    if response.recommendations:
        cleaned_recommendations = []
        for rec in response.recommendations:
            cleaned_recommendations.append(clean_content(rec))
        response.recommendations = cleaned_recommendations

    return response


def clean_content(content: str) -> str:
    """Remove any headers and extra formatting from content to prevent duplication."""
    if not content:
        return content

    # Remove common prefixes that cause duplication
    prefixes_to_remove = [
        'ANSWER:', 'Answer:', 'DETAILS:', 'Details:', 'RESULTS:', 'Results:',
        'SQL CODE:', 'SQL Code:', 'RECOMMENDATIONS:', 'Recommendations:',
        'DATABASE ANALYSIS:', 'Database Analysis:'
    ]

    cleaned_content = content.strip()

    # Remove prefixes from the beginning of content
    for prefix in prefixes_to_remove:
        if cleaned_content.startswith(prefix):
            cleaned_content = cleaned_content[len(prefix):].strip()
            break

    lines = cleaned_content.split('\n')
    cleaned_lines = []

    for line in lines:
        # Skip lines that are headers (start with #)
        if line.strip().startswith('#'):
            continue
        # Skip lines that are just section names
        if line.strip() in ['Details', 'Results', 'Explanation', 'Query Results', 'Database Size Analysis']:
            continue
        cleaned_lines.append(line)

    # Join lines and clean up extra whitespace
    cleaned_content = '\n'.join(cleaned_lines).strip()

    # Remove multiple consecutive newlines
    while '\n\n\n' in cleaned_content:
        cleaned_content = cleaned_content.replace('\n\n\n', '\n\n')

    return cleaned_content


def format_field_content(field_name: str, emoji: str, content: str) -> str:
    """Generic function to format any field with consistent structure."""
    # Create header with emoji
    header = f"### {emoji} {field_name}"

    # For Streamlit compatibility, use HTML for indentation instead of tabs
    # This ensures proper markdown rendering while maintaining visual hierarchy
    indented_content = content.replace("\n", "\n")

    # Return formatted field with proper structure
    return f"{header}\n\n{indented_content}"


def create_formatted_display(response: DBAResponse) -> str:
    """Create a formatted display using the already formatted individual fields."""
    formatted_parts = []

    # Generic field mapping with emojis and field names
    field_mappings = [
        ("answer", "📌", "Answer"),
        ("details", "📋", "Details"),
        ("results", "📊", "Results"),
        ("sql_code", "💻", "SQL Code"),
        ("recommendations", "�", "Recommendations")
    ]

    # Process each field with proper headers
    for field_attr, emoji, field_name in field_mappings:
        field_value = getattr(response, field_attr, None)

        if field_value:
            # Add header
            formatted_parts.append(f"#### {emoji} {field_name}")

            if field_attr == "recommendations" and isinstance(field_value, list):
                # Handle recommendations list
                if field_value:
                    recommendations_text = "\n".join([f"• {rec}" for rec in field_value])
                    formatted_parts.append(recommendations_text)
            elif field_attr == "sql_code":
                # Handle SQL code with proper formatting
                formatted_parts.append(f"```sql\n{field_value}\n```")
            else:
                # For other fields, use the content directly
                formatted_parts.append(field_value)

            # Add spacing between sections
            formatted_parts.append("")

    return "\n".join(formatted_parts)
