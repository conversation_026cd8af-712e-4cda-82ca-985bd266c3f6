"""
Prompts for src_tgt validation in database conversion.
"""
from typing import Dict
from Conversion_Agent.utils.database_names import get_database_specific_terms

def create_src_tgt_validation_prompt(source_context: Dict, target_error_context: Dict, corrected_statement: str, target_statements: list = None) -> str:
    """Create a focused validation prompt that checks if the specific deployment error was fixed."""
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    expert_title = db_terms['expert_title']

    return f"""You are a {expert_title}. Your task is to validate if the corrected statement properly fixes the specific deployment error without over-correcting other issues.

SOURCE STATEMENT:
{source_context.error_statement}

ORIGINAL TARGET STATEMENT (with error):
{target_error_context.error_statement}

CORRECTED TARGET STATEMENT:
{corrected_statement}

ADDITIONAL CONTEXT:
{f"Complete target code context available for analysis - examine the full structure to understand function signatures, parameter types, and return patterns" if target_statements else "Limited context available"}

ERROR-DRIVEN VALIDATION APPROACH:
Follow this systematic validation methodology:

STEP 1: ERROR RESOLUTION ANALYSIS (Primary)
- Does the corrected statement directly address the specific error that was reported?
- Are the syntax violations mentioned in the original error now resolved?
- Would deploying this corrected statement eliminate the reported deployment failure?
- **CRITICAL**: Is the corrected statement EXECUTABLE PostgreSQL code (not just comments)?
- **REJECT**: Any solution that only contains comments, explanations, or TODO notes
- **REQUIRE**: Working PostgreSQL code that performs the intended business operation

STEP 2: TARGET DATABASE COMPLIANCE (Secondary)
- Does the corrected statement follow correct PostgreSQL syntax rules?
- If this is within a function, does it respect the function signature requirements?
- Are parameter types (IN, OUT, INOUT) handled correctly according to PostgreSQL patterns?
- Is this the standard way to implement this construct in PostgreSQL?

STEP 3: BUSINESS LOGIC PRESERVATION (Tertiary)
- Does the corrected statement maintain the same business purpose as the source?
- Is the data flow and functionality equivalent to the source?
- Are the intended outcomes preserved?
- For target database-specific statements: Does the correction use appropriate target database expertise?

TASK:
1. Analyze if the corrected statement directly resolves the reported error
2. Validate target database syntax compliance and database-specific requirements
3. For statements with source equivalents: Confirm that business logic and functionality are preserved
4. For target database-specific statements: Validate that appropriate target database expertise was applied
5. Provide overall validation decision based on error resolution effectiveness

VALIDATION CRITERIA (Priority Order):

1. **ERROR RESOLUTION EFFECTIVENESS (Primary)**
   - Does the corrected statement directly fix the specific error that was reported?
   - Are all syntax violations mentioned in the error message now resolved?
   - Would this correction eliminate the deployment failure?
   - Is the error-causing element properly addressed?
   - **EXECUTABLE CODE REQUIREMENT**: Is the corrected statement actual PostgreSQL code that executes business logic?
   - **REJECT COMMENTS**: Does the solution contain only comments, explanations, or documentation?
   - **FUNCTIONAL IMPLEMENTATION**: Does the corrected statement perform the intended operation?

2. **TARGET DATABASE COMPLIANCE (Secondary)**
   - Does the corrected statement follow correct PostgreSQL syntax rules?
   - Are database-specific patterns correctly implemented?
   - For functions: Are parameter types and return patterns handled correctly?
   - Is this the proper PostgreSQL way to implement this construct?

3. **BUSINESS LOGIC PRESERVATION (Tertiary)**
   - Does the corrected statement perform the same operation as the source for ANY operation type including:
     * Data Manipulation: SELECT, INSERT, UPDATE, DELETE, MERGE, UPSERT operations
     * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME operations
     * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN operations
     * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN operations
     * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling
     * Cursor Operations: OPEN, FETCH, CLOSE cursor operations
     * Aggregate Operations: GROUP BY, HAVING, window functions, analytical functions
     * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTEs
     * Index Operations: CREATE/DROP INDEX, hints, optimization directives
     * Security Operations: GRANT, REVOKE, user/role management
     * System Operations: sequence generation, trigger definitions, view creation
   - Are the business logic and data transformations equivalent?
   - Are the expected results and functional outcomes the same?

FOCUSED VALIDATION CRITERIA:

1. **SPECIFIC ERROR RESOLUTION**
   - Does the corrected statement fix the exact error mentioned in the deployment error message?
   - Are syntax issues properly resolved for the specific error type?
   - If the same error pattern appeared multiple times, were ALL instances fixed?
   - Were different types of issues left untouched (as they should be)?
   - Is the statement now valid and executable for PostgreSQL?

2. **MINIMAL CORRECTION VALIDATION**
   - Was the correction minimal and targeted to the specific error?
   - Did the correction avoid fixing unrelated issues in the same statement?
   - Is the correction focused only on the deployment error mentioned?

3. **FUNCTIONAL PRESERVATION**
   - Is the original functionality and business logic preserved?
   - Does the corrected statement maintain the same business purpose as the source?

IMPORTANT GUIDELINES:
- PRIMARY FOCUS: Validate that the specific deployment error was fixed
- SECONDARY FOCUS: Confirm the correction was minimal and targeted (didn't over-correct)
- TERTIARY FOCUS: Ensure functional equivalence is preserved
- Use the deployment error message as the primary guide for validation success
- Validate that only the specific error type was fixed, not other unrelated issues

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0.0 and 1.0>,
  "explanation": "<focused validation analysis including: 1) Specific error resolution - how the corrected statement fixes the exact deployment error, 2) Minimal correction validation - whether the fix was targeted and didn't over-correct, 3) Functional preservation - confirmation that business logic is maintained, 4) Assessment of whether only the specific error type was fixed>",
  "error_resolution": {{
    "original_error_fixed": true/false,
    "syntax_valid": true/false,
    "functionality_preserved": true/false
  }},
  "src_tgt_assessment": {{
    "operation_equivalence": true/false,
    "data_transformation_equivalent": true/false,
    "business_logic_preserved": true/false
  }}
}}"""
