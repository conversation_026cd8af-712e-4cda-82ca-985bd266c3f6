trigger:
- Test

resources:
- repo: self

variables:
  dockerRegistryServiceConnection: 'QM_Agents'
  imageRepository: 'qmigrator_agents'
  containerRegistry: 'qmigtest.azurecr.io'
  dockerfilePath: '**/Dockerfile_QM_Test'
  tag: '$(Build.BuildId)'
  vmImageName: 'ubuntu-latest'

stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - task: Docker@2
      displayName: Build
      inputs:
        command: build
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
        arguments: '--build-arg UID=$(UID) --build-arg GID=$(GID) '

    - task: AquaSecurityOfficial.trivy-official.custom-build-release-task.trivy@1
      displayName: Image Scan
      # enabled: False
      inputs:
        image: qmigtest.azurecr.io/$(imageRepository):$(tag)
        # severities: CRITICAL,HIGH,MEDIUM,LOW
        # loginDockerConfig: true
        ignoreUnfixed: true
        options: '--scanners vuln'
        exitCode: 0
    
    - task: Docker@2
      displayName: push an image to container registry
      inputs:
        command: Push
        repository: $(imageRepository)
        dockerfile: $(dockerfilePath)
        containerRegistry: $(dockerRegistryServiceConnection)
        tags: |
          $(tag)
