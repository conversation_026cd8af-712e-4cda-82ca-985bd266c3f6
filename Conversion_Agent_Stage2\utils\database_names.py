"""
Utility functions for dynamic database names in Stage 2 QMigrator AI.

This module provides helper functions to get dynamic database names from configuration
and use them throughout Stage 2 application, making the system flexible for different
database migration scenarios.
"""





def split_migration_name(migration_name: str, remove_versions: bool = False):
    """
    Split migration name into source and target database names.

    Args:
        migration_name: Full migration name (e.g., "Oracle_Postgres14")
        remove_versions: If True, removes version numbers for clean database names

    Returns:
        tuple: (source_db, target_db)
        - If remove_versions=False: ("Oracle", "Postgres14")
        - If remove_versions=True: ("Oracle", "Postgres")
    """
    if not migration_name:
        raise ValueError("migration_name is required")

    if '_' not in migration_name:
        raise ValueError(f"Invalid migration_name format: {migration_name}. Expected format: 'SourceDB_TargetDB'")

    parts = migration_name.split('_', 1)  # Split only on first underscore
    source_db = parts[0]
    target_db = parts[1]

    if remove_versions:
        # Remove version numbers from database names for clean prompt usage
        import re
        # Remove trailing numbers/versions (e.g., "Postgres14" -> "Postgres", "Oracle19c" -> "Oracle")
        source_db = re.sub(r'\d+[a-zA-Z]*$', '', source_db)
        target_db = re.sub(r'\d+[a-zA-Z]*$', '', target_db)

    return source_db, target_db


def get_database_specific_terms(migration_name: str):
    """
    Get database-specific terminology for Stage 2 prompts from migration name.
    Uses clean database names without version numbers for prompts.

    Args:
        migration_name: Full migration name from request (e.g., "Oracle_Postgres14")

    Returns:
        dict: Database-specific terms for prompts with clean names
        Example: Oracle_Postgres14 -> source_db="Oracle", target_db="Postgres"
    """
    # Get clean database names without versions for prompts
    source_db, target_db = split_migration_name(migration_name, remove_versions=True)

    return {
        'source_db': source_db,           # "Oracle" (no version)
        'target_db': target_db,           # "Postgres" (no version)
        'migration_direction': f"{source_db} to {target_db}",  # "Oracle to Postgres"
        'migration_name': migration_name,  # "Oracle_Postgres14" (full name for file paths)
        'expert_title': f"{source_db} to {target_db} Module Responsibility Expert"
    }



