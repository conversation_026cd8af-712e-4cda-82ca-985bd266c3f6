"""
Streamlit Application for DBA Agent

This application provides a user-friendly interface for the DBA Agent
using Streamlit, similar to the conversion agent.
"""

import streamlit as st
from DBA_Agent.state.state import DBARequest
from DBA_Agent.utils.dba_processor import process_dba_query

# Page configuration
st.set_page_config(
    page_title="🗄️ DBA Agent",
    page_icon="🗄️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
    .response-container {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables."""
    # No session state variables needed currently
    pass


def display_header():
    """Display the application header."""
    st.markdown('<div class="main-header">🗄️ DBA Agent</div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Information about the agent
    st.markdown("""
    <div class="info-box">
        <h4>🎯 About DBA Agent</h4>
        <p>The DBA Agent helps you analyze database health, performance, and structure using natural language queries. 
        Ask questions about your database and get comprehensive answers with SQL code, results, and recommendations.</p>
    </div>
    """, unsafe_allow_html=True)


def display_sidebar():
    """Display the sidebar with configuration."""
    with st.sidebar:
        st.header("⚙️ Configuration")
        
        # Display current LLM provider
        try:
            from llm_config.config_manager import ConfigManager
            config_manager = ConfigManager()
            current_provider = config_manager.get_llm_provider()
            st.info(f"🤖 Current LLM: **{current_provider.upper()}**")
        except:
            st.warning("⚠️ Could not load LLM configuration")
        
        st.markdown("---")
        
        # Database Information
        st.header("🗄️ Database Info")
        st.markdown("""
        **Available Tools:**
        - 📊 Database size analysis
        - 🔍 Table and index information
        - ⚙️ Configuration parameters
        - 📈 Performance metrics
        - 🔧 Health checks
        
        **Example Queries:**
        - "What is the database size?"
        - "Show me the largest tables"
        - "Check database performance"
        - "List all indexes"
        """)


def process_dba_query_request(query, target_connection_id):
    """Process the DBA query request."""
    try:
        with st.spinner("🔍 Analyzing database..."):
            # Create DBA request
            request = DBARequest(query=query, target_connection_id=target_connection_id)
            
            # Process the query
            response = process_dba_query(request)
            
            return True, response
            
    except Exception as e:
        return False, f"Error processing query: {str(e)}"


def display_dba_response(response):
    """Display the DBA response in a formatted way with proper markdown rendering."""
    st.markdown('<div class="response-container">', unsafe_allow_html=True)

    # Add CSS for proper header hierarchy and content indentation
    st.markdown("""
    <style>
    .response-container h4 {
        font-size: 1.1em !important;
        margin-bottom: 10px;
        margin-top: 20px;
        font-weight: bold;
    }
    .response-container h4 + p,
    .response-container h4 + div,
    .response-container h4 + table,
    .response-container h4 + ul,
    .response-container h4 + pre {
        margin-left: 20px;
        font-size: 0.9em;
    }
    .response-container h1,
    .response-container h2,
    .response-container h3,
    .response-container h5,
    .response-container h6 {
        font-size: 0.95em !important;
        font-weight: normal !important;
        margin-left: 20px;
        margin-top: 10px;
        margin-bottom: 5px;
    }
    </style>
    """, unsafe_allow_html=True)

    # Display the formatted response using the formatted_display field
    if hasattr(response, 'formatted_display') and response.formatted_display:
        # Process the formatted display to ensure proper markdown rendering
        formatted_content = process_formatted_content_for_streamlit(response.formatted_display)
        st.markdown(formatted_content, unsafe_allow_html=True)
    else:
        # Fallback to individual fields if formatted_display is not available
        if response.answer:
            st.markdown(f"## {response.answer}")

        if response.details:
            st.markdown(response.details)

        if response.sql_code:
            st.markdown("### 💻 SQL Code")
            st.code(response.sql_code, language="sql")

        if response.results:
            st.markdown(response.results)

        if response.recommendations:
            st.markdown("### 💡 Recommendations")
            for rec in response.recommendations:
                st.markdown(f"- {rec}")

    st.markdown('</div>', unsafe_allow_html=True)


def process_formatted_content_for_streamlit(content: str) -> str:
    """Process formatted content to ensure proper markdown rendering in Streamlit."""
    # Remove tab indentation that interferes with markdown
    content = content.replace('\t', '')

    # Convert any headers within content to smaller headers to maintain hierarchy
    # Main section headers should be H3 (###), content headers should be H5 (#####)
    lines = content.split('\n')
    processed_lines = []
    in_content_section = False

    for line in lines:
        # Check if this is a main section header (with emoji)
        if line.startswith('#### ') and any(emoji in line for emoji in ['📌', '📋', '📊', '💻', '💡']):
            in_content_section = True
            processed_lines.append(line)
        # If we're in a content section and find other headers, make them smaller
        elif in_content_section and line.startswith('##') and not line.startswith('####'):
            # Convert ## to ##### to make content headers smaller
            if line.startswith('### '):
                processed_lines.append(line.replace('### ', '##### '))
            elif line.startswith('## '):
                processed_lines.append(line.replace('## ', '##### '))
            elif line.startswith('# '):
                processed_lines.append(line.replace('# ', '##### '))
            else:
                processed_lines.append(line)
        else:
            processed_lines.append(line)

    content = '\n'.join(processed_lines)

    # Ensure proper spacing between sections
    content = content.replace('\n\n\n', '\n\n')

    return content


def main():
    """Main application function."""
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar
    display_sidebar()
    
    # Main query form
    st.markdown('<div class="section-header">💬 Database Query</div>', unsafe_allow_html=True)
    
    with st.form("dba_query_form"):
        st.subheader("🔍 Ask Your Database Question")

        # Target Connection ID input
        target_connection_id = st.number_input(
            "Target Connection ID:",
            min_value=1,
            value=9,
            help="Database connection ID for dynamic database access"
        )

        query = st.text_area(
            "Enter your database query:",
            height=150,
            placeholder="Ask anything about your database (e.g., 'What is the database size?', 'Show me the largest tables', 'Check database performance')...",
            help="Use natural language to ask questions about database health, performance, structure, or configuration"
        )

        # Submit button
        submitted = st.form_submit_button("🚀 Analyze Database", use_container_width=True)
    
    # Process query when form is submitted
    if submitted:
        if not query.strip():
            st.error("❌ Please enter a database query.")
        else:
            # Process the query
            success, result = process_dba_query_request(query, target_connection_id)
            
            if success:
                st.markdown('<div class="section-header">📊 Analysis Results</div>', unsafe_allow_html=True)
                display_dba_response(result)
                
                # Show option to ask another question
                if st.button("🔄 Ask Another Question"):
                    st.rerun()
                    
            else:
                st.markdown(f'<div class="error-box">❌ {result}</div>', unsafe_allow_html=True)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem;">
        🗄️ DBA Agent | Powered by AI | Built with Streamlit
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
