from fastapi import APIRouter, HTTPException

# Import DBA agent components
from DBA_Agent.state.state import DBARequest, DBAResponse, DBAHistoryManager
from DBA_Agent.utils.dba_processor import process_dba_query

router = APIRouter()


@router.post("/dba-agent", response_model=DBAResponse)
def dba_agent(request: DBARequest):
    """
    Process database analysis queries using AI-driven DBA agent.

    This endpoint takes natural language queries about database health, performance,
    and structure, then uses specialized database tools and AI to provide comprehensive answers.
    """
    try:
        return process_dba_query(request)
    except Exception as e:
        print(f"❌ DBA agent endpoint failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "DBA agent workflow failed",
                "message": str(e)
            }
        )


@router.post("/clear-history")
def clear_dba_history():
    """
    Clear the DBA agent chat history.

    This endpoint clears all stored conversation history for the DBA agent.
    """
    try:
        DBAHistoryManager.clear_history()
        return {"message": "Chat history cleared successfully"}
    except Exception as e:
        print(f"❌ Failed to clear chat history: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to clear chat history",
                "message": str(e)
            }
        )