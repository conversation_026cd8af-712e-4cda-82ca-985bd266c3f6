from pydantic import BaseModel, Field
from config import Config


class AzureOpenAIConfig(BaseModel):
    """Azure OpenAI model configuration."""
    api_key: str = Field(
        default=Config.AZURE_OPENAI_API_KEY,
        description="Azure OpenAI API key"
    )
    endpoint: str = Field(
        default=Config.AZURE_OPENAI_ENDPOINT,
        description="Azure OpenAI endpoint"
    )
    deployment_name: str = Field(
        default=Config.AZURE_DEPLOYMENT_NAME,
        description="Azure OpenAI deployment name"
    )
    model_name: str = Field(
        default=Config.AZURE_OPENAI_MODEL,
        description="Azure OpenAI model name"
    )
    api_version: str = Field(
        default=Config.AZURE_OPENAI_API_VERSION,
        description="Azure OpenAI API version"
    )
    temperature: float = Field(
        default=float(Config.AZURE_OPENAI_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.AZURE_OPENAI_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class OpenAIConfig(BaseModel):
    """OpenAI model configuration."""
    api_key: str = Field(
        default=Config.OPENAI_API_KEY,
        description="OpenAI API key"
    )
    model_name: str = Field(
        default=Config.OPENAI_MODEL,
        description="OpenAI model name"
    )
    temperature: float = Field(
        default=float(Config.OPENAI_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.OPENAI_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class AnthropicConfig(BaseModel):
    """Anthropic model configuration."""
    api_key: str = Field(
        default=Config.ANTHROPIC_API_KEY,
        description="Anthropic API key"
    )
    model_name: str = Field(
        default=Config.ANTHROPIC_MODEL,
        description="Anthropic model name"
    )
    temperature: float = Field(
        default=float(Config.ANTHROPIC_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.ANTHROPIC_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class GroqConfig(BaseModel):
    """Groq model configuration."""
    api_key: str = Field(
        default=Config.GROQ_API_KEY,
        description="Groq API key"
    )
    model_name: str = Field(
        default=Config.GROQ_MODEL,
        description="Groq model name"
    )
    temperature: float = Field(
        default=float(Config.GROQ_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.GROQ_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class OllamaConfig(BaseModel):
    """Ollama model configuration."""
    base_url: str = Field(
        default=Config.OLLAMA_BASE_URL,
        description="Ollama base URL"
    )
    model_name: str = Field(
        default=Config.OLLAMA_MODEL,
        description="Ollama model name"
    )
    temperature: float = Field(
        default=float(Config.OLLAMA_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.OLLAMA_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class GeminiConfig(BaseModel):
    """Google Gemini model configuration."""
    api_key: str = Field(
        default=Config.GEMINI_API_KEY,
        description="Google API key for Gemini"
    )
    model_name: str = Field(
        default=Config.GEMINI_MODEL,
        description="Gemini model name"
    )
    temperature: float = Field(
        default=float(Config.GEMINI_TEMPERATURE),
        description="Temperature for generation"
    )
    max_tokens: int = Field(
        default=int(Config.GEMINI_MAX_TOKENS),
        description="Maximum number of tokens to generate"
    )


class ModelConfig(BaseModel):
    """Model configuration for all supported LLM providers."""
    azure_openai: AzureOpenAIConfig = Field(
        default_factory=AzureOpenAIConfig,
        description="Azure OpenAI configuration"
    )
    openai: OpenAIConfig = Field(
        default_factory=OpenAIConfig,
        description="OpenAI configuration"
    )
    anthropic: AnthropicConfig = Field(
        default_factory=AnthropicConfig,
        description="Anthropic configuration"
    )
    groq: GroqConfig = Field(
        default_factory=GroqConfig,
        description="Groq configuration"
    )
    ollama: OllamaConfig = Field(
        default_factory=OllamaConfig,
        description="Ollama configuration"
    )
    gemini: GeminiConfig = Field(
        default_factory=GeminiConfig,
        description="Google Gemini configuration"
    )


# Create a default model configuration
default_model_config = ModelConfig()
