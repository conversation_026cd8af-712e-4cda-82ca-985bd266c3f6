import psycopg2
from Conversion_Agent.utils.database_names import get_database_specific_terms


def deployToTargetDatabase(updated_target_code: str, target_db_credentials: dict) -> tuple:
    """
    Deploy the code to target database and check for errors.

    Args:
        updated_target_code: SQL code to execute
        target_db_credentials: Dictionary containing database connection details
                              Expected keys: 'name', 'password', 'host', 'db_name', 'port'

    Returns:
        tuple: (success: bool, error_message: str or None)
    """
    try:
        # Get dynamic database names
        db_terms = get_database_specific_terms()
        target_db = db_terms['target_db']

        try:
            # Validate required credentials
            required_fields = ["host", "port", "db_name", "name", "password"]
            missing_fields = [field for field in required_fields if not target_db_credentials.get(field)]

            if missing_fields:
                error_message = f"Missing required database credentials: {', '.join(missing_fields)}"
                print(f"❌ {error_message}")
                return False, error_message

            # Use dynamic credentials from request
            conn_params = {
                "host": target_db_credentials.get("host"),
                "port": target_db_credentials.get("port"),
                "database": target_db_credentials.get("db_name"),
                "user": target_db_credentials.get("name"),
                "password": target_db_credentials.get("password")
            }

            print(f"🔌 Connecting to {target_db} database at {conn_params['host']}:{conn_params['port']}...")
            conn = psycopg2.connect(**conn_params)
            print(f"🔌 Connected to {target_db} database...")
            conn.autocommit = False
            cursor = conn.cursor()
            cursor.execute(updated_target_code)
            conn.commit()
            cursor.close()
            conn.close()
            print("✅ SQL code executed successfully")
            return True, None

        except Exception as e:
            error_message = f"{target_db} Error: {e.pgerror}" if hasattr(e, 'pgerror') else str(e)
            print(f"❌ {target_db} execution failed: {error_message}")
            if 'conn' in locals() and conn:
                conn.rollback()
                conn.close()
            return False, error_message
    except Exception as e:
        print(f"❌ Deployment error: {str(e)}")
        return False, str(e)