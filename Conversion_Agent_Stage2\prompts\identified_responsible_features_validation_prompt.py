"""
Prompts for validating identified responsible features in Stage 2 conversion analysis.
"""
from typing import Dict, List, Any, Optional
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_identified_responsible_features_validation_prompt(
    identified_features: List[Any],  # Can be ResponsibleFeature objects or dicts
    original_source: str,
    ai_converted: str,
    actual_target: str,
    deployment_error: str,
    decrypted_modules: Dict[str, str],
    keyword_mapping: List[Dict[str, Any]] = None,
    validation_feedback: Optional[str] = None,
    db_terms: Optional[Dict[str, str]] = None
) -> str:
    """
    Create AI validation prompt for identified responsible features.

    Optimized to send ONLY what's needed for validation:
    - Identified features to validate
    - Basic conversion context (source, expected, actual, error)
    - ONLY the module code for identified features
    - Minimal keyword mapping (optional)

    Args:
        identified_features: List of features identified as responsible
        original_source: Original source database statement
        ai_converted: Expected target database statement from AI
        actual_target: Actual wrong target database statement
        deployment_error: Error message from deployment
        decrypted_modules: Dictionary of ONLY identified module contents
        keyword_mapping: Optional keyword-to-module mapping (minimal)
        validation_feedback: Previous validation feedback if any
        db_terms: Database-specific terms from migration name

    Returns:
        Optimized validation prompt with only necessary information
    """

    # Use provided database terms or fallback to config-based approach
    if db_terms is None:
        db_terms = get_database_specific_terms()

    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    # Format identified features for validation (simplified)
    identified_features_str = ""
    for i, feature in enumerate(identified_features, 1):
        # Handle ResponsibleFeature objects, dictionaries, and tuples
        if hasattr(feature, 'feature_name'):  # ResponsibleFeature object
            feature_name = feature.feature_name
            module_path = feature.module_path
        elif isinstance(feature, tuple) and len(feature) >= 2:  # Tuple format (feature_name, module_path)
            feature_name = feature[0]
            module_path = feature[1]
        else:  # Dictionary
            feature_name = feature.get('feature_name', 'Unknown')
            module_path = feature.get('module_path', 'Unknown')

        identified_features_str += f"{i}. {feature_name} -> {module_path}\n"

    # Include previous feedback if available (simplified)
    feedback_section = ""
    if validation_feedback:
        feedback_section = f"""
PREVIOUS FEEDBACK: {validation_feedback}
"""

    prompt = f"""
You are a {expert_title} Validation Expert. Validate if these identified features are truly responsible for the conversion failure.

{feedback_section}
FEATURES TO VALIDATE:
====================
{identified_features_str}

CONVERSION CONTEXT:
==================
{source_db} Source: {original_source}

Expected {target_db}: {ai_converted}

Actual {target_db}: {actual_target}

Error: {deployment_error}

MODULE CODE (IDENTIFIED FEATURES ONLY):
======================================
"""

    # Only include code for identified modules
    for module_name, module_code in decrypted_modules.items():
        prompt += f"\n--- {module_name.upper()} ---\n{module_code}\n"

    prompt += f"""
VALIDATION TASK:
===============
For each identified feature, answer: "Is this module truly responsible for the conversion failure?"

CRITERIA:
- Does module handle the failing syntax?
- Is module's purpose aligned with the error?
- Would fixing this module resolve the error?

OUTPUT (JSON):
=============
{{
  "validation_results": [
    {{
      "feature_name": "<name>",
      "module_path": "<path>",
      "is_valid": true/false,
      "validation_reason": "<why valid or invalid>"
    }}
  ],
  "overall_validation_passed": true/false,
  "validation_summary": "<summary>",
  "feedback_for_reidentification": "<feedback if validation failed>"
}}

Validate efficiently. Trust identification unless clearly wrong.
"""

    return prompt
