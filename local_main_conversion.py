"""
Local Main for Conversion Agent

This script runs the conversion agent locally with hardcoded values,
similar to main_ref.py but using the existing conversion agent workflow.
"""

import sys
import uuid
from typing import Dict, Any
from llm_config.config_manager import ConfigManager
from Conversion_Agent.workflow.graph_builder import G<PERSON><PERSON><PERSON><PERSON><PERSON>
from common.common import create_llm
from common.setup import Pre_SetupManager
from config import Config


def setup_application() -> Any:
    """
    Set up the Conversion Agent application with configuration and initialize the Language Model.

    This function handles the complete application initialization process for database
    migration workflows. It loads configuration settings from environment variables,
    validates the LLM provider selection, and initializes the appropriate AI client for
    SQL conversion tasks.

    The setup process includes:
        - Loading configuration from environment variables
        - Validating LLM provider availability
        - Initializing the selected AI provider with proper credentials
        - Preparing the LLM for database migration operations

    Returns:
        Any: Initialized LLM instance ready for database migration workflows

    Raises:
        Exception: If LLM initialization fails due to invalid credentials, network issues,
                  or unsupported provider configuration

    Example:
        >>> llm = setup_application()
        >>> # LLM is now ready for database migration tasks
    """
    config_manager = ConfigManager()
    llm_provider = config_manager.get_llm_provider()

    try:
        print(f"🔧 Attempting to initialize {llm_provider} LLM for database migration...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} client for SQL conversion")
        return llm
    except Exception as e:
        print(f"❌ Error initializing {llm_provider}: {str(e)}")
        raise


def run_conversion_workflow(llm: Any, source_code: str, target_code: str, deployment_error: str, max_attempt_per_statement: int = 5) -> Dict[str, Any]:
    """
    Execute the complete database migration workflow.

    This function orchestrates the entire AI-driven migration process using a sophisticated
    workflow that includes error analysis, source mapping, statement conversion, and validation.
    The workflow uses LangGraph for state management and supports iterative improvements
    until successful target database deployment.

    Workflow Steps:
        1. Split SQL statements for granular analysis
        2. Identify problematic target statements using hybrid AI/position-based approach
        3. Map target statements to corresponding source statements
        4. Convert statements using AI with source context
        5. Validate conversions and deploy to target database
        6. Iterate until successful deployment

    Args:
        llm (Any): Initialized Language Model instance for AI-driven SQL analysis
        source_code (str): Original source database code to be migrated
        target_code (str): Target database code with potential deployment errors
        deployment_error (str): Error message from target database deployment attempt

    Returns:
        Dict[str, Any]: Workflow execution results containing final state, iteration count,
                       deployment status, and complete audit trail

    Example:
        >>> llm = setup_application()
        >>> result = run_conversion_workflow(llm, source_sql, target_sql, error_msg)
        >>> print(f"Migration completed in {result['iteration_count']} iterations")
    """

    # Create a mock request object for setup manager (hardcoded values for local testing)
    class MockRequest:
        def __init__(self):
            self.tgt_object_id = 1
            self.target_connection_id = 9
            self.migration_name = 'Oracle_Postgres14'
            self.project_id = 1235
            self.objectname = 'FN_GETPATIENTDETAILS'
            self.cloud_category = 'Local'
            self.max_attempt_per_statement = max_attempt_per_statement
            self.run_number = 1
            self.target_schema_name = 'lab'
            self.object_type = 'Function'

    print("🔧 Setting up conversion environment...")
    # Initialize setup manager
    mock_request = MockRequest()
    setup_manager = Pre_SetupManager(Config, mock_request)
    print("🔧 Environment setup completed successfully.")

    # Get database names from environment variables (set by pre_setup_migration)
    source_db = Config.get_source_database()
    target_db = Config.get_target_database()
    print(f"📊 Source DB: {source_db} → Target DB: {target_db}")
    print(f"📁 Conversion files path: {setup_manager.conversion_path}")

    # Validate credentials like in API
    if setup_manager.target_db_credentials:
        print(f"🔐 Target DB credentials Fetched from API")
    else:
        print("⚠️ Target DB credentials not available")
        raise ValueError("Target DB credentials not available")
    if setup_manager.project_DB_details:
        print(f"🔐 Project DB credentials Fetched from API")
    else:
        print("⚠️ Project DB credentials not available")
        raise ValueError("Project DB credentials not available")

    # Initialize the workflow graph builder with the LLM
    graph_builder = GraphBuilder(llm)
    graph_builder.setup_graph()

    # Generate workflow visualization for debugging and documentation
    graph_builder.save_graph_image(graph_builder.graph)

    # Create a unique thread ID for this workflow execution to enable state tracking
    thread_id = f"thread_{uuid.uuid4()}"
    print(f"🔗 Using thread ID: {thread_id}")

    try:
        # Execute the complete migration workflow with all required parameters
        result = graph_builder.invoke_graph({
            "source_code": source_code,
            "target_code": target_code,
            "deployment_error": deployment_error,
            "conversion_path": setup_manager.conversion_path,
            "target_db_credentials": setup_manager.target_db_credentials,
            "project_db_credentials": setup_manager.project_DB_details,
            "iteration_count": 1,  # Initialize iteration count for tracking
            "max_attempt_per_statement": max_attempt_per_statement,
            "migration_name": mock_request.migration_name,
            "target_object_id": mock_request.tgt_object_id,
            "object_type": mock_request.object_type,
        }, thread_id=thread_id)

        print("🎉 Conversion workflow completed!")

        return result

    except Exception as e:
        print(f"❌ Conversion workflow failed: {str(e)}")
        raise


def main():
    """
    Main entry point for the Conversion Agent local execution.

    This function demonstrates the complete migration workflow using sample source and target
    database code with a deployment error. The inputs are hardcoded for easy local testing
    without requiring command-line arguments or user input.

    The main function:
        - Sets up the AI application with proper LLM configuration
        - Executes the complete migration workflow
        - Handles errors gracefully with detailed error reporting
        - Provides clear success/failure feedback

    Sample Data:
        - Source database stored procedure with complex business logic
        - Target database conversion with deployment errors
        - Actual target database error message for testing

    Raises:
        ValueError: If configuration is invalid or LLM provider is unsupported
        Exception: If unexpected errors occur during workflow execution
    """
    try:
        print("🚀 Starting Conversion Agent Local Execution...")
        print("=" * 60)
        
        # Sample source database code for testing the migration workflow
        # In production, this would be provided by users through the web interface
        source_code = """
CREATE OR REPLACE  FUNCTION ""LAB"".""FN_GETPATIENTDETAILS"" (IN_UHID IN VARCHAR)
  RETURN VARCHAR2 IS
  V_PATIENTDETAILS VARCHAR(500);
BEGIN
  SELECT (SELECT TLM.TITLETYPE
            FROM EHIS.TITLEMASTER TLM
           WHERE P.TITLE = TLM.TITLECODE) || ' ' || P.FIRSTNAME || ' ' ||
         P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
         (SELECT SM.SUFFIXNAME
            FROM EHIS.SUFFIXMASTER SM
           WHERE P.SUFIX = SM.SUFFIXCODE) || ' /' || LD.LOVDETAILVALUE || ' /' ||
         FLOOR(MONTHS_BETWEEN(SYSDATE, P.BIRTHDATE) / 12) || 'Yr' || ' ' ||
         floor(mod(months_between(sysdate, p.birthdate), 12)) || 'Mth' || ' ' ||
         trunc(sysdate -
               add_months(p.birthdate,
                          trunc(months_between(sysdate, p.birthdate) / 12) * 12 +
                          trunc(mod(months_between(sysdate, p.birthdate), 12)))) ||
         'Days'
  --TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')
    INTO V_PATIENTDETAILS
    FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
   WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
     AND (P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
         P.UHID = IN_UHID);
  RETURN V_PATIENTDETAILS;
END FN_GETPATIENTDETAILS;
        """

        target_code = """
set search_path to LAB;
create or replace function LAB.FN_GETPATIENTDETAILS(IN_UHID IN VARCHAR)
returns varchar
language plpgsql
security definer as $BODY$
declare
V_PATIENTDETAILS VARCHAR(500);
begin
set search_path to LAB;
SELECT(SELECT TLM.TITLETYPE
FROM EHIS.TITLEMASTER TLM
WHERE P.TITLE = TLM.TITLECODE)|| ' ' || P.FIRSTNAME || ' ' ||
P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
(SELECT SM.SUFFIXNAME
FROM EHIS.SUFFIXMASTER SM
WHERE P.SUFIX = SM.SUFFIXCODE)|| ' /' || LD.LOVDETAILVALUE || ' /' ||
FLOOR(public.months_between(current_timestamp(0)::timestamp, P.BIRTHDATE)/ 12)|| 'Yr' || ' ' ||
floor(mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate), 12))|| 'Mth' || ' ' ||
current_date -
p.birthdate + INTERVAL 'date_trunc('day',public.months_between(current_timestamp(0 Months'::timestamp, p.birthdate/ 12)* 12 +
mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate::date , 12))))||
'Days'
--TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')


into strict V_PATIENTDETAILS
FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
AND(P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
P.UHID = IN_UHID);
RETURN V_PATIENTDETAILS;
end;
$BODY$;
        """

        deployment_error = """
SQL Error [42601]: ERROR: syntax error at or near "',public.months_between(current_timestamp(0 Months'"
  Position: 752

Error position: line: 20 pos: 751

        """

        print("📊 Sample Data Loaded:")
        print(f"   - Source Code: {len(source_code)} characters")
        print(f"   - Target Code: {len(target_code)} characters")
        print(f"   - Deployment Error: {deployment_error.strip()}")
        print()

        # Setup the application
        llm = setup_application()
        
        # Run the conversion workflow with default max attempts per statement
        max_attempt_per_statement = 5  # Default value, can be modified for testing
        print("🔄 Starting conversion workflow...")
        print(f"📊 Max attempts per target statement: {max_attempt_per_statement}")
        result = run_conversion_workflow(llm, source_code, target_code, deployment_error, max_attempt_per_statement)

        print("\n🎉 Conversion workflow completed successfully!")
        

    except ValueError as e:
        print(f"\n❌ Configuration Error: {str(e)}")
        print("\nPlease check your configuration and try again.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected Error: {str(e)}")
        print("\nPlease report this issue with the error details above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
