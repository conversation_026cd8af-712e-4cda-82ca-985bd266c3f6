# Python-specific
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
*.dylib
*.egg
*.egg-info/
dist/
build/
eggs/
parts/
var/
sdist/
*.bak

# Virtual environment
.venv/
venv/
env/
ENV/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*.sublime-project
*.sublime-workspace

# MacOS system files
.DS_Store

# Windows system files
Thumbs.db
ehthumbs.db

# Python coverage reports
.coverage
.coverage.*
nosetests.xml
*.cover
*.tox

# Pytest
.cache/

# Jupyter Notebooks
.ipynb_checkpoints/

# Distribution / packaging
*.tar.gz
*.egg-info/
*.dist-info/

# MyPy
.mypy_cache/

# Pipenv
Pipfile.lock

# Flask
instance/
.webassets-cache/

# Django
*.log
*.pot
*.pyc
__pycache__/
db.sqlite3
media/
staticfiles/

# PyInstaller
*.spec

# Sphinx documentation
docs/_build/

# pytest
.pytest_cache/

# Black
*.bak

# .env files
.env
.env.*