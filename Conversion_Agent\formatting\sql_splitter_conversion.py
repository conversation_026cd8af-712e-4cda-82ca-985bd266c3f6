import re
import pandas as pd
# from common.comments_handling import retrieve_singlequote_data, retrieve_comments_with_rules, replace_comment_markers, replace_single_quote_markers


import numpy as np


def retrieve_singlequote_data(source_data):
    single_quote_comment_dictionary = {}

    arrow_comments = re.findall(r"\-+\>", source_data)
    for comment in arrow_comments:
        source_data = source_data.replace(comment, 'arrow_quad_marker', 1)

    singlequote_data = re.findall(r"\'.*?\'", source_data, flags=re.DOTALL)

    if len(singlequote_data):
        counter = 0
        for i in singlequote_data:
            if i.strip().lower() in source_data.lower().strip():
                unique_marker = 'single_quote_quad_marker_' + str(counter) + '_us'
                source_data = source_data.replace(i, unique_marker, 1)
                single_quote_comment_dictionary[unique_marker] = i
            counter = counter + 1
    else:
        source_data = source_data
    return source_data, single_quote_comment_dictionary


def retrieve_comments_with_rules(source_data, comment_identifiers):
    source_data = re.sub(r'\-\-+', '--', source_data)
    source_data = source_data.replace('--','\n--')

    comments_dictionary = {}
    if type(comment_identifiers) is not float and not isinstance(comment_identifiers,np.float64):
        comment_identifiers = re.sub(' +', ' ', comment_identifiers).lower()
        current_comment_identifiers_split_list = comment_identifiers.split('&')

        counter = 0
        for comment_rule in current_comment_identifiers_split_list:
            start_key = comment_rule.split('end:')[0].split('start:')[1].strip()
            end_key = comment_rule.split('end:')[1].strip()

            if '-' in start_key:
                start_key = start_key.replace('-', '\-')
            elif '/' in start_key or '*' in start_key:
                start_key = start_key.replace('/', '\/').replace('*', '\*')
            if '*/' in end_key:
                end_key = end_key.replace('*/', '\*/')
            comments_data_list = re.findall(rf"{start_key}[\s\S]*?{end_key}", source_data)

            for comments_data in comments_data_list:
                if '-' in start_key:
                    source_data = source_data.replace(comments_data,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data
                else:
                    comments_data_modified = '/*' + comments_data.replace('/*', '').replace('*/',
                                                                                            '').replace('/',
                                                                                                        '') + '*/'
                    source_data = source_data.replace(comments_data, comments_data_modified)
                    source_data = source_data.replace(comments_data_modified,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data_modified
                counter += 1
    return source_data, comments_dictionary


def replace_comment_markers(data, comment_dictionary):
    if len(comment_dictionary):
        for key, value in comment_dictionary.items():
            try:
                value = value.replace('\\', 'backslash_quad_marker')
                data = re.sub(rf'\b{key}\b', value + '\n', data, flags=re.IGNORECASE | re.DOTALL)
                data = data.replace('backslash_quad_marker', '\\')
            except Exception as e:
                print(f'Error in replace_comment_markers at {key} : {str(e)}')
                data = data.replace(key, value + '\n', 1)
    else:
        data = data
    return data


def replace_single_quote_markers(data, single_quote_comment_dictionary):
    if len(single_quote_comment_dictionary):
        # print(single_quote_comment_dictionary)

        for key, value in single_quote_comment_dictionary.items():
            try:
                # data = data.replace(key, value, 1)
                # value = re.escape(value)
                # if re.search(r'\(|\)|\'|\"|\]|\[',value,flags=re.DOTALL):
                #     data = re.sub(f'{re.escape(key)}', re.escape(value), data, flags=re.IGNORECASE | re.DOTALL)
                # else:
                data = re.sub(f'{re.escape(key)}', value, data, flags=re.IGNORECASE | re.DOTALL)
                # data = re.sub(rf'{key}', value, data, flags=re.IGNORECASE | re.DOTALL)
            except Exception as e:
                print(f'Error in replace_single_quote_markers at {key} : {str(e)}')
    else:
        data = data
    return data


def create_sub_objects(source_data, object_path, rules_data, objects_data):
    object_path_rules = rules_data[(rules_data['Object_Path'].str.contains(pat=object_path)) & (
        rules_data['Object_Path'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    object_path_data = objects_data[(objects_data['Object_Id'].str.contains(pat=object_path)) & (
        objects_data['Object_Id'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    if not object_path_data.empty:
        if object_path_data.iloc[0]['Object_Category'] == 'Group':
            group_rules_data = rules_data[
                rules_data['Object_Path'].isin(objects_data[(objects_data['Object_Category'] == 'Group')]['Object_Id'])]
        else:
            group_rules_data = rules_data[rules_data['Object_Path'].isin(object_path_data['Object_Id'])]

        objects_identifier_set = set()
        if not group_rules_data.empty:
            for object_identifier in group_rules_data.iloc[:, 1]:
                if isinstance(object_identifier, str):
                    object_identifier_list = object_identifier.split('&')
                    for element in object_identifier_list:
                        start_key = re.sub(' +', ' ', element.lower().split('end:')[0].split('start:')[1].strip())
                        objects_identifier_set.add(start_key.lower())

    object_tuple_list =[]
    source_data = re.sub(r'\bas\b', '\nas', source_data, flags=re.I)
    source_data = re.sub(r'\bis\b', '\nis', source_data, flags=re.I)

    source_data_list = source_data.split('\n')

    for rules_index, rules_record in object_path_rules.iterrows():
        index_dictionary_list = []
        object_identifier = re.sub(' +', ' ', rules_record[1].lower())
        object_identifier_list = [i.strip() for i in object_identifier.split('&') if i != '']

        for element in object_identifier_list:
            index_dictionary = {}
            start_key = re.sub(' +', ' ', element.split('end:')[0].split('start:')[1].strip())
            end_key = re.sub(' +', ' ', element.split('end:')[1].strip())
            if '|' in start_key:
                start_index = next(
                    (i for i, line in enumerate(source_data_list) for word in start_key.split('|')
                     if re.search(rf'\\b{word}\\b', line.strip().lower())),
                    None
                )
                if start_index is not None:
                    if end_key.strip() != 'object-1':
                        end_index = next(
                            (index for index, data in enumerate(source_data_list)
                             if end_key.strip().lower() in data.strip().lower() and index > start_index),
                            None
                        )

                    else:
                        end_index = next(
                            (index - 1 for index, data1 in enumerate(source_data_list)
                             for element in list(objects_identifier_set)
                             if re.sub(' +', ' ',
                                       element).strip().lower() in data1.strip().lower() and index > start_index),
                            None
                        )

                    if end_index is not None and end_index > start_index:
                        index_dictionary[start_index] = end_index
                    else:
                        index_dictionary[start_index] = len(source_data_list) - 1

            elif ';+1'in start_key:
                for index, data in enumerate(source_data_list):
                    if ';' in data.strip().lower():

                        if end_key.strip() != 'object-1':
                            end_index = next((i for i in range(index + 1, len(source_data_list))
                                              if end_key.strip().lower() in source_data_list[i].strip().lower()), None)

                        else:
                            end_index = next((
                                index1 for index1, data1 in enumerate(source_data_list[index:], start=index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))),
                                None)
                            end_index = end_index - 1

                        if 'create or replace' not in source_data_list[index].strip().lower() and end_index is not None:
                            index_dictionary[index + 1] = end_index
                        else:
                            index_dictionary[index + 1] = len(source_data_list) - 1
            else:
                for index, data in enumerate(source_data_list):
                    if start_key.strip().lower() in data.strip().lower():
                        start_index = index
                        end_search_list = []
                        if end_key.strip() == 'object-1':
                            end_index = next(
                                index1 for index1, data1 in enumerate(source_data_list[start_index:], start=start_index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))
                            ) - 1
                            end_search_list.append(end_index)

                        elif '|' in end_key:
                            check_index_list = [
                                index for index, data in enumerate(source_data_list)
                                if any(re.search(rf'\b{word}\b', data.strip().lower()) for word in
                                       end_key.strip().split('|'))
                            ]
                            end_index = next((
                                index for index in check_index_list if index > start_index), None)

                            # if 'is|as' == end_key or 'as|is' == end_key:
                            #     end_index -= 1
                            if end_index >= start_index:
                                end_search_list.append(end_index)
                        else:
                            end_index = next((
                                index for index, data in enumerate(source_data_list)
                                if end_key.strip().lower() in data.strip().lower() and index > start_index
                            ),None)

                            end_search_list.append(end_index)

                        if end_search_list:
                            index_dictionary[start_index] = min(end_search_list)
                        else:
                            index_dictionary[start_index] = len(source_data_list) - 1

            index_dictionary_list.append(index_dictionary)

        set_index_dictionary_list = [
            {key: dict_i[key]}
            for dict_i in index_dictionary_list if len(dict_i) > 0
            for key in dict_i
        ]
        set_index_dictionary_list = list({frozenset(d.items()): d for d in set_index_dictionary_list}.values())
        index_dictionary_list = sorted(set_index_dictionary_list, key=lambda d: list(d.keys()))

        start_index_list = [key for dict_i in index_dictionary_list for key in dict_i]
        end_index_list = [value for dict_i in index_dictionary_list for value in dict_i.values()]

        if len(set(end_index_list)) == 1:
            index_dictionary_list = [{min(start_index_list): end_index_list[0]}]

        object_name = rules_record[4].split('/')[-1]
        for dict in index_dictionary_list:
            for key, value in dict.items():
                if key and value:
                    tuple_defination = '\n'.join([source_data_list[i] for i in range(key, value + 1)])
                    created_tuple = (object_name, str(tuple_defination))
                    object_tuple_list.append(created_tuple)
    return object_tuple_list

# source_data = """
# CREATE OR REPLACE  FUNCTION ""LAB"".""FN_GETPATIENTDETAILS"" (IN_UHID IN VARCHAR)
#   RETURN VARCHAR2 IS
#   V_PATIENTDETAILS VARCHAR(500);
# BEGIN
#   SELECT (SELECT TLM.TITLETYPE
#             FROM EHIS.TITLEMASTER TLM
#            WHERE P.TITLE = TLM.TITLECODE) || ' ' || P.FIRSTNAME || ' ' ||
#          P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
#          (SELECT SM.SUFFIXNAME
#             FROM EHIS.SUFFIXMASTER SM
#            WHERE P.SUFIX = SM.SUFFIXCODE) || ' /' || LD.LOVDETAILVALUE || ' /' ||
#          FLOOR(MONTHS_BETWEEN(SYSDATE, P.BIRTHDATE) / 12) || 'Yr' || ' ' ||
#          floor(mod(months_between(sysdate, p.birthdate), 12)) || 'Mth' || ' ' ||
#          trunc(sysdate -
#                add_months(p.birthdate,
#                           trunc(months_between(sysdate, p.birthdate) / 12) * 12 +
#                           trunc(mod(months_between(sysdate, p.birthdate), 12)))) ||
#          'Days'
#   --TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')
#     INTO V_PATIENTDETAILS
#     FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
#    WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
#      AND (P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
#          P.UHID = IN_UHID);
#   RETURN V_PATIENTDETAILS;
# END FN_GETPATIENTDETAILS;
# """

# dynamic_rules_path = 'C:/Repos/New_Repos/Web-Agents/Dynamic_Rules' + '/' + 'Oracle_Postgres14' + '/' + 'Oracle_Postgres14' + '.csv'
# rules_data = pd.read_csv(dynamic_rules_path)

# objects_excel = 'C:/Repos/New_Repos/Web-Agents/Conversion_Modules' + '/' + 'Oracle_Postgres14' + '/' + 'Oracle_Postgres14' + '_objects_data.csv'
# objects_data = pd.read_csv(objects_excel)

# comment_identifiers = rules_data[rules_data['Object_Path'] == 'Function/Statement']['Comment_Identifiers'].values.tolist()[0]


# source_data = re.sub(' +', ' ', source_data)
# comments_data_list = re.findall(r'\/\*[\s\S]*?\*\/', source_data)
# for comment_data in comments_data_list:
#     modified_comment_data = comment_data.replace('/*', '/*\n', 1).replace('*/', '\n*/', 1)
#     source_data = source_data.replace(comment_data, modified_comment_data)

# source_data, single_quote_comment_dict = retrieve_singlequote_data(source_data)
# source_data, comment_dict = retrieve_comments_with_rules(source_data, comment_identifiers)
# source_data = re.sub(r' +', ' ', source_data)

# source_data = re.sub(r'CREATE\sProc\b', 'CREATE PROCEDURE', source_data, flags=re.IGNORECASE | re.DOTALL)
# source_data = source_data.replace('&lt ;', '&lt;').replace('&lt;', '<')
# source_data = source_data.replace('&gt ;', '&gt;').replace('&gt;', '>')
# source_data = re.sub(r':\s*=\s*', ':= ', source_data, flags=re.DOTALL | re.IGNORECASE)
# source_data = re.sub(r' +', ' ', source_data)


# object_tuple_list = create_sub_objects(source_data, 'Function/Statement', rules_data, objects_data)

# statements = [(index, f"{obj[1]}") for index, obj in enumerate(object_tuple_list, 1) if obj[1].strip() != '']
# print(statements)
# masked_data = '\n'.join([f"{obj[1]}" for obj in object_tuple_list])
# with_comments_output = replace_comment_markers(masked_data, comment_dict)
# with_comments_output = replace_single_quote_markers(with_comments_output, single_quote_comment_dict)
# print(with_comments_output)


def split_sql_statements(sql_script, migration_name, object_type):
    dynamic_rules_path = 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
    rules_data = pd.read_csv(dynamic_rules_path)

    objects_excel = 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
    objects_data = pd.read_csv(objects_excel)

    if object_type in ['Function','Procedure']:
        object_path = f'{object_type}/Statement'
    else:
        object_path = object_type

    comment_identifiers = rules_data[rules_data['Object_Path'] == object_path]['Comment_Identifiers'].values.tolist()[0]

    sql_script = re.sub(' +', ' ', sql_script)
    comments_data_list = re.findall(r'\/\*[\s\S]*?\*\/', sql_script)
    for comment_data in comments_data_list:
        modified_comment_data = comment_data.replace('/*', '/*\n', 1).replace('*/', '\n*/', 1)
        sql_script = sql_script.replace(comment_data, modified_comment_data)

    sql_script, comment_dict = retrieve_comments_with_rules(sql_script, comment_identifiers)
    sql_script, single_quote_comment_dict = retrieve_singlequote_data(sql_script)
    sql_script = re.sub(r' +', ' ', sql_script)

    sql_script = re.sub(r'CREATE\sProc\b', 'CREATE PROCEDURE', sql_script, flags=re.IGNORECASE | re.DOTALL)
    sql_script = sql_script.replace('&lt ;', '&lt;').replace('&lt;', '<')
    sql_script = sql_script.replace('&gt ;', '&gt;').replace('&gt;', '>')
    sql_script = re.sub(r':\s*=\s*', ':= ', sql_script, flags=re.DOTALL | re.IGNORECASE)
    sql_script = re.sub(r' +', ' ', sql_script)


    object_tuple_list = create_sub_objects(sql_script, object_path, rules_data, objects_data)

    statements = [(index, f"{obj[1]}") for index, obj in enumerate(object_tuple_list, 1)]

    # Remove empty statement only if it's the last statement
    if statements and statements[-1][1].strip() == '':
        statements = statements[:-1]

    # Restore comments in the correct order: first regular comments, then single quote comments
    restored_statements = []
    for index, statement in statements:
        with_comments_output = replace_comment_markers(statement, comment_dict)
        with_comments_output = replace_single_quote_markers(with_comments_output, single_quote_comment_dict)
        restored_statements.append((index, with_comments_output))

    return restored_statements



print(split_sql_statements(
  """set search_path to LAB;
create or replace function LAB.FN_GETPATIENTDETAILS(IN_UHID IN VARCHAR)
returns varchar
language plpgsql
security definer as $BODY$
declare
V_PATIENTDETAILS VARCHAR(500);
begin
set search_path to LAB;
SELECT(SELECT TLM.TITLETYPE
FROM EHIS.TITLEMASTER TLM
WHERE P.TITLE = TLM.TITLECODE)|| ' ' || P.FIRSTNAME || ' ' ||
P.MIDDLENAME || ' ' || P.LASTNAME || ' ' ||
(SELECT SM.SUFFIXNAME
FROM EHIS.SUFFIXMASTER SM
WHERE P.SUFIX = SM.SUFFIXCODE)|| ' /' || LD.LOVDETAILVALUE || ' /' ||
FLOOR(public.months_between(current_timestamp(0)::timestamp, P.BIRTHDATE)/ 12)|| 'Yr' || ' ' ||
floor(mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate), 12))|| 'Mth' || ' ' ||
current_date -
p.birthdate + INTERVAL 'date_trunc('day',public.months_between(current_timestamp(0 Months'::timestamp, p.birthdate/ 12)* 12 +
mod(public.months_between(current_timestamp(0)::timestamp, p.birthdate::date , 12))))||
'Days'
--TO_CHAR(P.BIRTHDATE,'DD-MON-YYYY')


into strict V_PATIENTDETAILS
FROM REGISTRATION.PATIENT P, EHIS.LOVDETAIL LD
WHERE LD.LOVDETAILID = TO_NUMBER(P.GENDER)
AND(P.EMERGENCYNO = IN_UHID OR P.PREREGISTRATIONNO = IN_UHID OR
P.UHID = IN_UHID);
RETURN V_PATIENTDETAILS;
end;
$BODY$;""",
  "Oracle_Postgres14",
  "Function"  
))