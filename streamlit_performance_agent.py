"""
Streamlit Application for Performance Agent

This application provides a user-friendly interface for the Performance Agent
using Streamlit, similar to the DBA agent.
"""

import streamlit as st
from Performance_Agent.state.state import PerformanceRequest
from Performance_Agent.utils.performance_processor import process_performance_query

# Page configuration
st.set_page_config(
    page_title="⚡ Performance Agent",
    page_icon="⚡",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2E86AB;
        margin: 1.5rem 0 1rem 0;
        border-bottom: 2px solid #2E86AB;
        padding-bottom: 0.5rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin: 0.5rem 0;
    }
    
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        color: #155724;
    }
    
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        color: #721c24;
    }
    
    .info-box {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        border-radius: 5px;
        padding: 1rem;
        margin: 1rem 0;
        color: #0c5460;
    }
</style>
""", unsafe_allow_html=True)


def display_performance_response(response):
    """Display the performance analysis response with proper markdown rendering."""
    # Add CSS for proper header hierarchy and content indentation
    st.markdown("""
    <style>
    .performance-container h4 {
        font-size: 1.1em !important;
        margin-bottom: 10px;
        margin-top: 20px;
        font-weight: bold;
    }
    .performance-container h4 + p,
    .performance-container h4 + div,
    .performance-container h4 + table,
    .performance-container h4 + ul,
    .performance-container h4 + pre {
        margin-left: 20px;
        font-size: 0.9em;
    }
    .performance-container h1,
    .performance-container h2,
    .performance-container h3,
    .performance-container h5,
    .performance-container h6 {
        font-size: 0.95em !important;
        font-weight: normal !important;
        margin-left: 20px;
        margin-top: 10px;
        margin-bottom: 5px;
    }
    </style>
    """, unsafe_allow_html=True)

    st.markdown('<div class="performance-container">', unsafe_allow_html=True)

    if response.formatted_display:
        # Process the formatted display to ensure proper markdown rendering
        formatted_content = process_performance_content_for_streamlit(response.formatted_display)
        st.markdown(formatted_content, unsafe_allow_html=True)
    else:
        # Fallback to individual sections
        if response.answer:
            st.markdown('<div class="section-header">📌 Performance Analysis</div>', unsafe_allow_html=True)
            st.markdown(response.answer)

        if response.details:
            st.markdown('<div class="section-header">📋 Detailed Analysis</div>', unsafe_allow_html=True)
            st.markdown(response.details)

        if response.results:
            st.markdown('<div class="section-header">📊 Performance Metrics</div>', unsafe_allow_html=True)
            st.markdown(response.results)

        if response.sql_code:
            st.markdown('<div class="section-header">💻 SQL Code</div>', unsafe_allow_html=True)
            st.code(response.sql_code, language="sql")

        if response.recommendations:
            st.markdown('<div class="section-header">💡 Performance Recommendations</div>', unsafe_allow_html=True)
            for i, rec in enumerate(response.recommendations, 1):
                st.markdown(f"**{i}.** {rec}")

    st.markdown('</div>', unsafe_allow_html=True)


def process_performance_content_for_streamlit(content: str) -> str:
    """Process formatted content to ensure proper markdown rendering in Streamlit."""
    # Remove tab indentation that interferes with markdown
    content = content.replace('\t', '')

    # Convert any headers within content to smaller headers to maintain hierarchy
    # Main section headers should be H3 (###), content headers should be H5 (#####)
    lines = content.split('\n')
    processed_lines = []
    in_content_section = False

    for line in lines:
        # Check if this is a main section header (with emoji)
        if line.startswith('#### ') and any(emoji in line for emoji in ['📌', '📋', '📊', '💻', '💡']):
            in_content_section = True
            processed_lines.append(line)
        # If we're in a content section and find other headers, make them smaller
        elif in_content_section and line.startswith('##') and not line.startswith('####'):
            # Convert ## to ##### to make content headers smaller
            if line.startswith('### '):
                processed_lines.append(line.replace('### ', '##### '))
            elif line.startswith('## '):
                processed_lines.append(line.replace('## ', '##### '))
            elif line.startswith('# '):
                processed_lines.append(line.replace('# ', '##### '))
            else:
                processed_lines.append(line)
        else:
            processed_lines.append(line)

    content = '\n'.join(processed_lines)

    # Ensure proper spacing between sections
    content = content.replace('\n\n\n', '\n\n')

    return content


def show_sidebar():
    """Display the sidebar with performance information and examples."""
    with st.sidebar:
        st.markdown('<div class="main-header">⚡ Performance Agent</div>', unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Performance Information
        st.header("⚡ Performance Analysis")
        st.markdown("""
        **Available Tools:**
        - 📊 Performance metrics analysis

        **Example Queries:**
        - "Show me current performance metrics"
        - "What's the cache hit ratio?"
        - "How many active connections are there?"
        """)


def process_performance_query_request(query, target_connection_id):
    """Process the performance query request."""
    try:
        with st.spinner("⚡ Analyzing performance..."):
            # Create Performance request
            request = PerformanceRequest(query=query, target_connection_id=target_connection_id)
            
            # Process the query
            response = process_performance_query(request)
            
            return True, response
            
    except Exception as e:
        return False, f"Error processing performance query: {str(e)}"


def main():
    """Main function to run the Performance Agent Streamlit app."""
    
    # Show sidebar
    show_sidebar()
    
    # Main content area
    st.markdown('<div class="main-header">⚡ Database Performance Agent</div>', unsafe_allow_html=True)
    
    st.markdown("""
    <div class="info-box">
        🎯 <strong>Welcome to the Performance Agent!</strong><br>
        Ask questions about database performance, identify bottlenecks, and get optimization recommendations.
        Use natural language to analyze performance metrics, slow queries, and system health.
    </div>
    """, unsafe_allow_html=True)
    
    # Performance query form
    with st.form("performance_query_form"):
        st.subheader("⚡ Ask Your Performance Question")

        # Target Connection ID input
        target_connection_id = st.number_input(
            "Target Connection ID:",
            min_value=1,
            value=9,
            help="Database connection ID for dynamic database access"
        )

        query = st.text_area(
            "Enter your performance query:",
            height=150,
            placeholder="Ask anything about database performance (e.g., 'Show me current performance metrics', 'What's the cache hit ratio?', 'How many active connections are there?')...",
            help="Use natural language to ask questions about database performance metrics and connection statistics"
        )

        # Submit button
        submitted = st.form_submit_button("🚀 Analyze Performance", use_container_width=True)
    
    # Process query when form is submitted
    if submitted:
        if not query.strip():
            st.error("❌ Please enter a performance query.")
        else:
            # Process the query
            success, result = process_performance_query_request(query, target_connection_id)
            
            if success:
                st.markdown('<div class="section-header">📊 Performance Analysis Results</div>', unsafe_allow_html=True)
                display_performance_response(result)
                
                # Show option to ask another question
                if st.button("🔄 Ask Another Question"):
                    st.rerun()
                    
            else:
                st.markdown(f'<div class="error-box">❌ {result}</div>', unsafe_allow_html=True)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem;">
        ⚡ Performance Agent | Powered by AI | Built with Streamlit
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
