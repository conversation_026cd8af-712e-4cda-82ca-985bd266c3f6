from typing import List, Any
from langchain_ollama import ChatOllama
from langchain_core.language_models.chat_models import BaseChatModel
from llm_config.config_manager import ConfigManager


class OllamaLLM:
    """Ollama LLM implementation using LangChain."""

    def __init__(self, config_manager: ConfigManager):
        """Initialize the Ollama LLM with configuration.

        Args:
            config_manager: Configuration manager containing Ollama settings
        """
        self.config_manager = config_manager
        ollama_config = config_manager.models_config.ollama
        self.base_url = ollama_config.base_url
        self.model_name = ollama_config.model_name

        # Initialize the Ollama client
        self.client = ChatOllama(
            base_url=self.base_url,
            model=self.model_name,
            temperature=ollama_config.temperature,
            num_predict=ollama_config.max_tokens,
        )
        print(f"Successfully initialized Ollama client with model {self.model_name}")

    def bind_tools(self, tools: List[Any], **kwargs) -> BaseChatModel:
        """Bind tools to the LLM.

        Args:
            tools: List of tools to bind to the LLM
            **kwargs: Additional keyword arguments

        Returns:
            The LLM client with tools bound to it
        """
        return self.client.bind_tools(tools)

    def get_llm(self) -> BaseChatModel:
        """Get the underlying LLM client.

        Returns:
            The Ollama chat model client
        """
        return self.client