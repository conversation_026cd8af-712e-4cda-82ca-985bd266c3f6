[project]
name = "web-agents"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langchain-openai==0.3.19",
    "langchain-anthropic==0.3.15",
    "langchain-groq==0.3.2",
    "langchain-community==0.3.24",
    "langgraph==0.4.8",
    "pillow==11.2.1",
    "pydantic==2.11.5",
    "python-dotenv==1.1.0",
    "langchain-google-genai==2.1.5",
    "scikit-learn==1.7.0",
    "langchain-ollama==0.3.3",
    "streamlit==1.45.1",
    "openpyxl==3.1.5",
    "pandas==2.3.0",
    "uvicorn[standard]==0.34.3",
    "fastapi==0.115.12",
    "python-multipart==0.0.20",
    "pyjwt==2.10.1",
    "psycopg2-binary==2.9.10",
    "aiohappyeyeballs==2.6.1",
    "aiohttp==3.12.9",
    "aiosignal==1.3.2",
    "altair==5.5.0",
    "annotated-types==0.7.0",
    "anthropic==0.52.2",
    "anyio==4.9.0",
    "attrs==25.3.0",
    "blinker==1.9.0",
    "cachetools==5.5.2",
    "certifi==2025.4.26",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "colorama==0.4.6",
    "dataclasses-json==0.6.7",
    "distro==1.9.0",
    "et-xmlfile==2.0.0",
    "filetype==1.2.0",
    "frozenlist==1.6.2",
    "gitdb==4.0.12",
    "gitpython==3.1.44",
    "google-ai-generativelanguage==0.6.18",
    "google-api-core==2.25.0",
    "google-auth==2.40.3",
    "googleapis-common-protos==1.70.0",
    "greenlet==3.2.3",
    "groq==0.26.0",
    "grpcio==1.72.1",
    "grpcio-status==1.72.1",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httptools==0.6.4",
    "httpx==0.28.1",
    "httpx-sse==0.4.0",
    "idna==3.10",
    "jinja2==3.1.6",
    "jiter==0.10.0",
    "joblib==1.5.1",
    "jsonpatch==1.33",
    "jsonpointer==3.0.0",
    "jsonschema==4.24.0",
    "jsonschema-specifications==2025.4.1",
    "langchain==0.3.25",
    "langchain-core==0.3.64",
    "langchain-text-splitters==0.3.8",
    "langgraph-checkpoint==2.0.26",
    "langgraph-prebuilt==0.2.2",
    "langgraph-sdk==0.1.70",
    "langsmith==0.3.45",
    "markupsafe==3.0.2",
    "marshmallow==3.26.1",
    "multidict==6.4.4",
    "mypy-extensions==1.1.0",
    "narwhals==1.41.0",
    "numpy==2.2.6",
    "ollama==0.5.1",
    "openai==1.84.0",
    "orjson==3.10.18",
    "ormsgpack==1.10.0",
    "packaging==24.2",
    "propcache==0.3.1",
    "proto-plus==1.26.1",
    "protobuf==6.31.1",
    "pyarrow==20.0.0",
    "pyasn1==0.6.1",
    "pyasn1-modules==0.4.2",
    "pydantic-core==2.33.2",
    "pydantic-settings==2.9.1",
    "pydeck==0.9.1",
    "python-dateutil==2.9.0.post0",
    "pytz==2025.2",
    "pyyaml==6.0.2",
    "referencing==0.36.2",
    "regex==2024.11.6",
    "requests==2.32.3",
    "requests-toolbelt==1.0.0",
    "rpds-py==0.25.1",
    "rsa==4.9.1",
    "scipy==1.15.3",
    "six==1.17.0",
    "smmap==5.0.2",
    "sniffio==1.3.1",
    "sqlalchemy==2.0.41",
    "starlette==0.46.2",
    "tenacity==9.1.2",
    "threadpoolctl==3.6.0",
    "tiktoken==0.9.0",
    "toml==0.10.2",
    "tornado==6.5.1",
    "tqdm==4.67.1",
    "typing-extensions==4.14.0",
    "typing-inspect==0.9.0",
    "typing-inspection==0.4.1",
    "tzdata==2025.2",
    "urllib3==2.4.0",
    "watchdog==6.0.0",
    "watchfiles==1.0.5",
    "websockets==15.0.1",
    "xxhash==3.5.0",
    "yarl==1.20.0",
    "zstandard==0.23.0",
    "cryptography>=45.0.4",
    "pycryptodome>=3.23.0",
]
