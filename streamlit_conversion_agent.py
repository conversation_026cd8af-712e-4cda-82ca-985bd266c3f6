"""
Streamlit Application for Conversion Agent

This application provides a user-friendly interface for the Conversion Agent
using Streamlit, similar to the DBA agent in main_ref.py.
"""

import streamlit as st
import uuid
from llm_config.config_manager import ConfigManager
from Conversion_Agent.workflow.graph_builder import GraphBuilder
from common.common import create_llm
from common.setup import Pre_SetupManager
from config import Config

# Page configuration
st.set_page_config(
    page_title="🔄 Conversion Agent",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #ff7f0e;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        margin: 1rem 0;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        margin: 1rem 0;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables."""
    # No session state variables needed currently
    pass

def create_conversion_agent():
    """Create and initialize the conversion agent."""
    try:
        # Setup the LLM
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        llm = create_llm(llm_provider, config_manager)

        # Initialize graph builder
        graph_builder = GraphBuilder(llm)
        graph_builder.setup_graph()
        
        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        return graph_builder, llm
    except Exception as e:
        st.error(f"❌ Failed to initialize Conversion Agent: {str(e)}")
        return None, None

def display_header():
    """Display the application header."""
    st.markdown('<div class="main-header">🔄 Conversion Agent</div>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Information about the agent
    st.markdown("""
    <div class="info-box">
        <h4>🎯 About Conversion Agent</h4>
        <p>The Conversion Agent helps you migrate and convert code between different platforms, databases, or frameworks.
        <strong>Required fields:</strong> source code, target code, deployment errors, and object name for intelligent conversion assistance.
        The agent will create organized files and Excel reports in your specified environment (Local or Cloud).</p>
    </div>
    """, unsafe_allow_html=True)

def display_sidebar():
    """Display the sidebar with configuration."""
    with st.sidebar:
        st.header("⚙️ Configuration")

        # Display current LLM provider
        try:
            config_manager = ConfigManager()
            current_provider = config_manager.get_llm_provider()
            st.info(f"🤖 Current LLM: **{current_provider.upper()}**")
        except:
            st.warning("⚠️ Could not load LLM configuration")



def process_conversion(source_code, target_code, deployment_error, max_attempt_per_statement,
                     tgt_object_id, target_connection_id, migration_name, project_id, objectname,
                     cloud_category, run_number, target_schema_name, object_type, graph_builder):
    """Process the conversion request using the graph builder."""
    try:
        with st.spinner("🔄 Setting up conversion environment..."):
            # Create a mock request object for setup manager
            class MockRequest:
                def __init__(self):
                    self.tgt_object_id = tgt_object_id
                    self.target_connection_id = target_connection_id
                    self.migration_name = migration_name
                    self.project_id = project_id
                    self.objectname = objectname
                    self.cloud_category = cloud_category
                    self.max_attempt_per_statement = max_attempt_per_statement
                    self.run_number = run_number
                    self.target_schema_name = target_schema_name
                    self.object_type = object_type

            # Initialize setup manager
            mock_request = MockRequest()
            setup_manager = Pre_SetupManager(Config, mock_request)
            st.success("🔧 Environment setup completed successfully.")

            # Get database names from environment variables (set by pre_setup_migration)
            source_db = Config.get_source_database()
            target_db = Config.get_target_database()
            st.info(f"📊 Source DB: {source_db} → Target DB: {target_db}")
            st.info(f"📁 Conversion files path: {setup_manager.conversion_path}")

            # Validate credentials like in API
            if setup_manager.target_db_credentials:
                st.success(f"🔐 Target DB credentials Fetched from API")
            else:
                st.error("⚠️ Target DB credentials not available")
                raise ValueError("Target DB credentials not available")

            if setup_manager.project_DB_details:
                st.success(f"🔐 Project DB credentials Fetched from API")
            else:
                st.error("⚠️ Project DB credentials not available")
                raise ValueError("Project DB credentials not available")

        with st.spinner("🔄 Processing conversion..."):
            # Create a unique thread ID for this workflow execution
            thread_id = f"thread_{uuid.uuid4()}"
            st.info(f"🔗 Using thread ID: {thread_id}")

            try:
                # Execute the complete migration workflow with all required parameters
                graph_builder.invoke_graph({
                    "source_code": source_code,
                    "target_code": target_code,
                    "deployment_error": deployment_error,
                    "conversion_path": setup_manager.conversion_path,
                    "target_db_credentials": setup_manager.target_db_credentials,
                    "project_db_credentials": setup_manager.project_DB_details,
                    "iteration_count": 1,
                    "max_attempt_per_statement": max_attempt_per_statement,
                    "migration_name": migration_name,
                    "target_object_id": tgt_object_id,
                    "object_type": object_type,
                }, thread_id=thread_id)

                st.success("🎉 Conversion workflow completed!")

                return True, "🎉 Execution completed successfully!"

            except Exception as e:
                st.error(f"❌ Conversion workflow failed: {str(e)}")
                raise

    except ValueError as e:
        return False, f"Configuration Error: {str(e)}"

    except Exception as e:
        return False, f"Unexpected Error: {str(e)}"

def main():
    """Main application function."""
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar
    display_sidebar()
    
    # Initialize conversion agent
    graph_builder, _ = create_conversion_agent()

    if not graph_builder:
        st.error("❌ Cannot proceed without a valid Conversion Agent. Please check your configuration.")
        return

    # Main input form
    st.markdown('<div class="section-header">📝 Conversion Input</div>', unsafe_allow_html=True)
    
    with st.form("conversion_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📄 Source Code")
            source_code = st.text_area(
                "Enter your source code:",
                height=200,
                placeholder="Paste your source code here...",
                help="The original code you want to convert"
            )
        
        with col2:
            st.subheader("🎯 Target Code")
            target_code = st.text_area(
                "Enter your target code:",
                height=200,
                placeholder="Paste your target code here...",
                help="The target code that needs to be fixed or converted"
            )
        
        st.subheader("⚠️ Deployment Error")
        deployment_error = st.text_area(
            "Enter deployment errors:",
            height=100,
            placeholder="Paste any error messages or issues encountered...",
            help="Errors encountered during deployment or conversion (required)"
        )

        # Configuration Settings
        st.subheader("⚙️ Configuration Settings")

        # First row - Migration settings
        col1, col2, col3 = st.columns(3)

        with col1:
            migration_name = st.selectbox(
                "Migration Type:",
                options=["Oracle_Postgres14", "SQLServer_Postgres14", "MySQL_Postgres14"],
                index=0,
                help="Select the source to target database migration type"
            )

        with col2:
            project_id = st.number_input(
                "Project ID:",
                min_value=1,
                value=1235,
                step=1,
                help="Project identifier for file organization"
            )

        with col3:
            tgt_object_id = st.number_input(
                "Target Object ID:",
                min_value=1,
                value=1,
                step=1,
                help="Target object identifier"
            )

        # Second row - Object and connection settings
        col1, col2, col3 = st.columns(3)

        with col1:
            objectname = st.text_input(
                "Object Name:",
                value="Employee",
                help="Name of the database object being migrated"
            )

        with col2:
            target_connection_id = st.number_input(
                "Target Connection ID:",
                min_value=1,
                value=9,
                step=1,
                help="Database connection identifier for target database"
            )

        with col3:
            cloud_category = st.selectbox(
                "Environment:",
                options=["Local", "Cloud"],
                index=0,
                help="Select deployment environment (Local or Cloud)"
            )

        # Third row - Database settings
        col1, col2, col3 = st.columns(3)

        with col1:
            run_number = st.number_input(
                "Run Number:",
                min_value=1,
                value=1,
                step=1,
                help="Run number for the migration"
            )

        with col2:
            target_schema_name = st.text_input(
                "Target Schema Name:",
                value="public",
                help="Target schema name for the migration"
            )

        with col3:
            object_type = st.text_input(
                "Object Type:",
                value="PROCEDURE",
                help="Type of the object being migrated (e.g., PROCEDURE, FUNCTION, TABLE, VIEW, TRIGGER, etc.)"
            )

        # Fourth row - Advanced settings
        col1, col2 = st.columns([1, 3])

        with col1:
            max_attempt_per_statement = st.number_input(
                "Max Attempts per Statement:",
                min_value=1,
                max_value=20,
                value=5,
                step=1,
                help="Maximum number of attempts allowed per target statement before moving to next statement or ending workflow"
            )

        with col2:
            st.markdown("""
            <div style="margin-top: 25px; padding: 10px; background-color: #f0f2f6; border-radius: 5px;">
                <small><strong>ℹ️ How it works:</strong><br>
                • Each target statement gets up to this many attempts<br>
                • If same statement fails repeatedly, workflow ends after max attempts<br>
                • If different statement is identified, attempts reset to 1<br>
                • Higher values = more persistent fixing, but longer execution time</small>
            </div>
            """, unsafe_allow_html=True)

        # Submit button
        submitted = st.form_submit_button("🚀 Start Conversion", use_container_width=True)
    
    # Process conversion when form is submitted
    if submitted:
        if not source_code.strip():
            st.error("❌ Please provide source code to convert.")
        elif not target_code.strip():
            st.error("❌ Please provide target code.")
        elif not deployment_error.strip():
            st.error("❌ Please provide deployment error information.")
        elif not objectname.strip():
            st.error("❌ Please provide object name.")
        elif not object_type.strip():
            st.error("❌ Please provide object type.")
        else:
            # Process the conversion with all required parameters
            success, message = process_conversion(
                source_code,
                target_code,
                deployment_error,
                max_attempt_per_statement,
                tgt_object_id,
                target_connection_id,
                migration_name,
                project_id,
                objectname,
                cloud_category,
                run_number,
                target_schema_name,
                object_type,
                graph_builder
            )

            if success:
                st.markdown(f'<div class="success-box">✅ {message}</div>', unsafe_allow_html=True)

                # Show success message with option to start new conversion
                if st.button("🔄 Start New Conversion"):
                    st.rerun()

            else:
                st.markdown(f'<div class="error-box">❌ {message}</div>', unsafe_allow_html=True)
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem;">
        🔄 Conversion Agent | Powered by QMigrator AI
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
