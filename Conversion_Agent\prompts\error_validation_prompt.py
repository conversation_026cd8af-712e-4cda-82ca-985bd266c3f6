"""
Prompts for error validation in database conversion.
"""
from typing import Dict
from Conversion_Agent.utils.database_names import get_database_specific_terms

def create_error_validation_prompt(target_error_context: Dict, error_message: str) -> str:
    """
    Creates a prompt for validating the identified error statement.

    This function creates a prompt that instructs the LLM to validate if the identified
    error statement is indeed causing the specified error.

    Args:
        target_error_context: Dictionary containing the error context (before, error, after statements)
        error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']
    migration_direction = db_terms['migration_direction']

    return f"""You are a {expert_title} with deep expertise in both {source_db} and {target_db} database systems. Your task is to validate if the identified statement is causing the reported error during {migration_direction} migration.

ERROR MESSAGE:
{error_message}

CONTEXT STATEMENTS:
Before Error (#{target_error_context.before_statement_number}):
{target_error_context.before_statement}

Error Statement (#{target_error_context.error_statement_number}):
{target_error_context.error_statement}

After Error (#{target_error_context.after_statement_number}):
{target_error_context.after_statement}

TASK:
1. Analyze the error message carefully
2. Examine the identified error statement in context
3. Determine if this statement is likely to cause the error described in the error message
4. Provide a confidence score and explanation

ANALYSIS APPROACH:
- Check if the error message directly references elements in the identified statement
- Look for syntax issues, data type mismatches, or semantic errors for ANY database operation type including:
  * Data Manipulation: INSERT, UPDATE, DELETE, SELECT, MERGE, UPSERT operation errors
  * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME syntax issues
  * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN problems
  * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN errors
  * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling issues
  * Cursor Operations: OPEN, FETCH, CLOSE cursor statement problems
  * Aggregate Operations: GROUP BY, HAVING, window functions, analytical function errors
  * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTE syntax issues
  * Index Operations: CREATE/DROP INDEX, hints, optimization directive problems
  * Security Operations: GRANT, REVOKE, user/role management errors
  * System Operations: sequence generation, trigger definitions, view creation issues

ERROR PATTERN VALIDATION:
- Function name mismatches between database dialects (e.g., SYSDATE vs current_timestamp)
- Data type incompatibilities (NUMBER vs NUMERIC vs INT, VARCHAR2 vs VARCHAR)
- String manipulation function syntax differences (SUBSTR vs SUBSTRING, CONCAT vs ||)
- Mathematical and statistical function name variations
- XML/JSON processing function compatibility issues
- Sequence generation syntax problems (NEXTVAL vs nextval())
- Exception handling syntax incompatibilities
- Schema/object naming issues and case sensitivity problems
- Parameter count or type mismatches in function calls
- Cursor lifecycle management errors
- Transaction isolation and locking mechanism issues
- Stored procedure/function declaration and parameter syntax problems
- Trigger syntax and event handling differences
- Index creation syntax and optimization hint variations
- Consider how the statement interacts with the database schema and object references
- Evaluate if the error could be caused by statements before or after instead
- Assess cross-database dialect compatibility issues

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0-1>,
  "explanation": "<comprehensive validation analysis including: 1) Detailed examination of the error message and its specific components, 2) Line-by-line analysis of the identified error statement, 3) Specific correlation between error details and statement elements, 4) Analysis of context statements and their potential contribution, 5) Detailed explanation of the database operation causing the issue, 6) Assessment of {migration_direction} conversion problems, 7) Reasoning for validation decision with supporting evidence>"
}}

IMPORTANT:
- Focus on the specific error message and its relation to the identified statement
- Consider syntax, data types, and other potential issues
- Provide comprehensive and detailed analysis with specific technical evidence"""
