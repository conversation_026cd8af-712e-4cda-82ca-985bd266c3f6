from typing import List, Any
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_core.language_models.chat_models import BaseChatModel
from llm_config.config_manager import ConfigManager


class AnthropicLLM:
    """Anthropic LLM implementation using LangChain."""

    def __init__(self, config_manager: ConfigManager):
        """Initialize the Anthropic LLM with configuration.

        Args:
            config_manager: Configuration manager containing Anthropic settings
        """
        self.config_manager = config_manager
        anthropic_config = config_manager.models_config.anthropic
        self.api_key = anthropic_config.api_key
        self.model_name = anthropic_config.model_name

        # Initialize the Anthropic client
        self.client = ChatAnthropic(
            api_key=self.api_key,
            model=self.model_name,
            temperature=anthropic_config.temperature,
            max_tokens=anthropic_config.max_tokens,
        )

    def bind_tools(self, tools: List[Any], **kwargs) -> BaseChatModel:
        """Bind tools to the LLM.

        Args:
            tools: List of tools to bind to the LLM
            **kwargs: Additional keyword arguments

        Returns:
            The LLM client with tools bound to it
        """
        return self.client.bind_tools(tools)

    def get_llm(self) -> BaseChatModel:
        """Get the underlying LLM client.

        Returns:
            The Anthropic chat model client
        """
        return self.client