# Use the official Python image as the base image
FROM python:3.12-slim

# Set the working directory in the container
WORKDIR /app

# Copy the application files into the container
COPY . /app

ARG GID
ARG UID

RUN echo "GID: $GID" && \
    echo "UID: $UID"

ENV UID=${UID} \
    GID=${GID}

# Install dependencies
RUN pip install --no-cache-dir --upgrade pip
RUN pip install --no-cache-dir -r requirements.txt

RUN if [ "${GID}" != "0" ]; then \
    groupadd --system --gid "${GID}" "app"; \
    fi

RUN adduser --gid "${GID}" --disabled-password --gecos "" --uid "${UID}" appuser && chown -R appuser /app
USER appuser


# Command to run the application
CMD ["python", "main.py"]
