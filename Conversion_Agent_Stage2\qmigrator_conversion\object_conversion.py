import os, sys, re, glob
import importlib.util
import pandas as pd
import numpy as np
from config import Config
from cryptography.fernet import Fernet
import warnings
warnings.filterwarnings("ignore")

def create_sub_objects(source_data, object_path, rules_data, objects_data):
    object_path_rules = rules_data[(rules_data['Object_Path'].str.contains(pat=object_path)) & (
        rules_data['Object_Path'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    object_path_data = objects_data[(objects_data['Object_Id'].str.contains(pat=object_path)) & (
        objects_data['Object_Id'].apply(lambda x: len(x.split('/')) == len(object_path.strip().split('/'))))]

    if not object_path_data.empty:
        if object_path_data.iloc[0]['Object_Category'] == 'Group':
            group_rules_data = rules_data[
                rules_data['Object_Path'].isin(objects_data[(objects_data['Object_Category'] == 'Group')]['Object_Id'])]
        else:
            group_rules_data = rules_data[rules_data['Object_Path'].isin(object_path_data['Object_Id'])]

        objects_identifier_set = set()
        if not group_rules_data.empty:
            for object_identifier in group_rules_data.iloc[:, 1]:
                if isinstance(object_identifier, str):
                    object_identifier_list = object_identifier.split('&')
                    for element in object_identifier_list:
                        start_key = re.sub(' +', ' ', element.lower().split('end:')[0].split('start:')[1].strip())
                        objects_identifier_set.add(start_key.lower())

    object_tuple_list =[]
    source_data = re.sub(r'\bas\b', '\nas', source_data, flags=re.I)
    source_data = re.sub(r'\bis\b', '\nis', source_data, flags=re.I)

    source_data_list = source_data.split('\n')


    for _, rules_record in object_path_rules.iterrows():
        index_dictionary_list = []
        object_identifier = re.sub(' +', ' ', rules_record.iloc[1].lower())
        object_identifier_list = [i.strip() for i in object_identifier.split('&') if i != '']


        for element in object_identifier_list:
            index_dictionary = {}
            start_key = re.sub(' +', ' ', element.split('end:')[0].split('start:')[1].strip())
            end_key = re.sub(' +', ' ', element.split('end:')[1].strip())
            if '|' in start_key:
                start_index = next(
                    (i for i, line in enumerate(source_data_list) for word in start_key.split('|')
                     if re.search(rf'\b{word}\b', line.strip().lower())),
                    None
                )
                if start_index is not None:
                    if end_key.strip() != 'object-1':
                        end_index = next(
                            (index for index, data in enumerate(source_data_list)
                             if end_key.strip().lower() in data.strip().lower() and index > start_index),
                            None
                        )

                    else:
                        end_index = next(
                            (index - 1 for index, data1 in enumerate(source_data_list)
                             for element in list(objects_identifier_set)
                             if re.sub(' +', ' ',
                                       element).strip().lower() in data1.strip().lower() and index > start_index),
                            None
                        )

                    if end_index is not None and end_index > start_index:
                        index_dictionary[start_index] = end_index
                    else:
                        index_dictionary[start_index] = len(source_data_list) - 1

            elif ';+1'in start_key:
                for index, data in enumerate(source_data_list):
                    if ';' in data.strip().lower():

                        if end_key.strip() != 'object-1':
                            end_index = next((i for i in range(index + 1, len(source_data_list))
                                              if end_key.strip().lower() in source_data_list[i].strip().lower()), None)

                        else:
                            end_index = next((
                                index1 for index1, data1 in enumerate(source_data_list[index:], start=index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))),
                                None)
                            end_index = end_index - 1

                        if 'create or replace' not in source_data_list[index].strip().lower() and end_index is not None:
                            index_dictionary[index + 1] = end_index
                        else:
                            index_dictionary[index + 1] = len(source_data_list) - 1
            else:
                for index, data in enumerate(source_data_list):
                    if start_key.strip().lower() in data.strip().lower():


                        start_index = index
                        end_search_list = []
                        if end_key.strip() == 'object-1':
                            end_index = next(
                                index1 for index1, data1 in enumerate(source_data_list[start_index:], start=start_index)
                                if any(element in data1.strip().lower() for element in list(objects_identifier_set))
                            ) - 1
                            end_search_list.append(end_index)

                        elif '|' in end_key:
                            check_index_list = [
                                index for index, data in enumerate(source_data_list)
                                if any(re.search(rf'\b{word}\b', data.strip().lower()) for word in
                                       end_key.strip().split('|'))
                            ]
                            end_index = next((
                                index for index in check_index_list if index > start_index), None)

                            # if 'is|as' == end_key or 'as|is' == end_key:
                            #     end_index -= 1
                            if end_index >= start_index:
                                end_search_list.append(end_index)
                        else:
                            end_index = next((
                                index for index, data in enumerate(source_data_list)
                                if end_key.strip().lower() in data.strip().lower() and index > start_index
                            ),None)

                            end_search_list.append(end_index)

                        if end_search_list:
                            index_dictionary[start_index] = min(end_search_list)

                        else:
                            index_dictionary[start_index] = len(source_data_list) - 1


            index_dictionary_list.append(index_dictionary)

        set_index_dictionary_list = [
            {key: dict_i[key]}
            for dict_i in index_dictionary_list if len(dict_i) > 0
            for key in dict_i
        ]
        set_index_dictionary_list = list({frozenset(d.items()): d for d in set_index_dictionary_list}.values())
        index_dictionary_list = sorted(set_index_dictionary_list, key=lambda d: list(d.keys()))

        start_index_list = [key for dict_i in index_dictionary_list for key in dict_i]
        end_index_list = [value for dict_i in index_dictionary_list for value in dict_i.values()]

        if len(set(end_index_list)) == 1:
            index_dictionary_list = [{min(start_index_list): end_index_list[0]}]

        object_name = rules_record.iloc[4].split('/')[-1]
        for dict in index_dictionary_list:
            for key, value in dict.items():
                if key is not None and value is not None:
                    tuple_defination = '\n'.join([source_data_list[i] for i in range(key, value + 1)])
                    created_tuple = (object_name, str(tuple_defination))
                    object_tuple_list.append(created_tuple)

    return object_tuple_list


def retrieve_comments_with_rules(source_data, comment_identifiers):
    source_data = re.sub(r'\-\-+', '--', source_data)
    source_data = source_data.replace('--','\n--')

    comments_dictionary = {}
    if type(comment_identifiers) is not float and not isinstance(comment_identifiers,np.float64):
        comment_identifiers = re.sub(' +', ' ', comment_identifiers).lower()
        current_comment_identifiers_split_list = comment_identifiers.split('&')

        counter = 0
        for comment_rule in current_comment_identifiers_split_list:
            start_key = comment_rule.split('end:')[0].split('start:')[1].strip()
            end_key = comment_rule.split('end:')[1].strip()

            if '-' in start_key:
                start_key = start_key.replace('-', r'\-')
            elif '/' in start_key or '*' in start_key:
                start_key = start_key.replace('/', r'\/').replace('*', r'\*')
            if '*/' in end_key:
                end_key = end_key.replace('*/', r'\*/')
            comments_data_list = re.findall(rf"{start_key}[\s\S]*?{end_key}", source_data)

            for comments_data in comments_data_list:
                if '-' in start_key:
                    source_data = source_data.replace(comments_data,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data
                else:
                    comments_data_modified = '/*' + comments_data.replace('/*', '').replace('*/',
                                                                                            '').replace('/',
                                                                                                        '') + '*/'
                    source_data = source_data.replace(comments_data, comments_data_modified)
                    source_data = source_data.replace(comments_data_modified,
                                                    ' comment_quad_marker_' + str(counter) + '_us' + ' \n')
                    comments_dictionary['comment_quad_marker_' + str(counter) + '_us'] = comments_data_modified
                counter += 1
    return source_data, comments_dictionary


def replace_comment_markers(data, comment_dictionary):
    if len(comment_dictionary):
        for key, value in comment_dictionary.items():
            try:
                value = value.replace('\\', 'backslash_quad_marker')
                data = re.sub(rf'\b{key}\b', value + '\n', data, flags=re.IGNORECASE | re.DOTALL)
                data = data.replace('backslash_quad_marker', '\\')
            except Exception as e:
                print(f'Error in replace_comment_markers at {key} : {str(e)}')
                data = data.replace(key, value + '\n', 1)
    else:
        data = data
    return data


def add_execute_keyword(query):
    if re.search(r'execute\s*immediate\s*', query, flags=re.IGNORECASE | re.DOTALL):
        query = re.sub(r'execute\s*immediate\s*', r"execute ", query, flags=re.IGNORECASE | re.DOTALL)
    if not re.search(r'\bexecute\s*\'', query, flags=re.IGNORECASE | re.DOTALL):
        check = re.findall(r"\'\s*(?:\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\balter\b).*?;", query,
                           flags=re.IGNORECASE | re.DOTALL)
        for i in list(set(check)):
            if re.search(r'\bselect\b', i, flags=re.DOTALL | re.I):
                if re.search(r'\bfrom\b', i, flags=re.DOTALL | re.I):
                    query = query.replace(i, 'Execute ' + i)
            else:
                query = query.replace(i, 'Execute ' + i)
    query_mod = re.findall(r'\bexecute\s*\'\s*(?:insert\b|update\b|delete\s*from\b|delete)\s*\'\s*\;', query,
                           flags=re.DOTALL | re.I)
    for q in query_mod:
        q1 = re.sub(r'\bexecute\b', '', q, flags=re.DOTALL | re.I)
        query = query.replace(q, q1)
    query = re.sub(r'\:\s*\=\s*execute\b', ' := ', query, flags=re.DOTALL | re.I)
    return query


def call_statement_handling(source_data, conversion_file_path):
    pacakages_path = conversion_file_path.replace('Conversion_Files.xlsx', 'Package_Procedure')
    package_procedures = []
    if os.path.exists(pacakages_path):
        package_procedures = os.listdir(pacakages_path)
        package_procedures = [i.replace('.sql', '').strip().split('-')[1].strip().upper() for i in package_procedures]
        package_procedures = list(set(package_procedures))
    code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')

    code_objects_df['Combined_Column'] = code_objects_df['Schema_Name'] + '.' + code_objects_df[
        'Object_Name'].str.replace('.', '_', regex=False)
    code_objects_df_all = code_objects_df
    code_objects_df = code_objects_df[['Combined_Column', 'Object_Type']].drop_duplicates()

    reserved_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                      'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP', 'EXCEPTION', 'IF',
                      'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                      'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                      'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                      'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                      'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT', 'RETURN',
                      'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR', 'SUBSTR',
                      'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX', 'AVG',
                      'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                      'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'DBMS_OUTPUT.ENABLE',
                      'DBMS_OUTPUT.PUT']

    source_data = re.sub(r' +', ' ', source_data)
    begin_end_block = re.findall(r'BEGIN.*END', source_data, flags=re.IGNORECASE | re.DOTALL)
    if begin_end_block:
        modified_begin_block = begin_end_block[0]
        code_objects_list1 = re.findall(r'\s+(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list_2 = re.findall(r'\bBEGIN\s*(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list = code_objects_list1 + code_objects_list_2
        code_objects_list = [i.split('(')[0].strip() if '(' in i else i.strip() for i in code_objects_list]
        code_objects_list = [i for i in code_objects_list if i.strip() not in reserved_words]
        for code_object_data in set(code_objects_list + package_procedures):
            if '.' not in code_object_data:
                code_object_type = \
                    code_objects_df_all[
                        code_objects_df_all['Object_Name'] == code_object_data.upper().replace('"', '').strip()][
                        'Object_Type']

            else:
                code_object_type = \
                    code_objects_df[
                        code_objects_df['Combined_Column'] == code_object_data.upper().replace('"', '').strip()][
                        'Object_Type']
            if not code_object_type.empty:
                if code_object_type.iloc[0] in ['PROCEDURE', 'PACKAGE_PROCEDURE']:
                    modified_begin_block = re.sub(rf'\b{code_object_data}', 'CALL ' + code_object_data,
                                                  modified_begin_block, flags=re.DOTALL | re.I)
            if code_object_data in package_procedures and re.search(code_object_data, modified_begin_block,
                                                                    flags=re.DOTALL | re.I):
                modified_begin_block = re.sub(rf'(?:\s{code_object_data}\b|\b{code_object_data}\b)',
                                              'CALL ' + code_object_data,
                                              modified_begin_block, flags=re.DOTALL | re.I)

        source_data = source_data.replace(begin_end_block[0], modified_begin_block)

    source_data = re.sub(r'\bcall\s*call\b', 'call', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bselect\s*select\b', 'select', source_data, flags=re.IGNORECASE | re.DOTALL)
    source_data = re.sub(r'\bend\s*call\b', ' end ', source_data, flags=re.IGNORECASE | re.DOTALL)
    end_loop_mod = re.findall(r'\bend\s*loop\s*\w+\s*;', source_data, flags=re.DOTALL | re.I)
    if end_loop_mod:
        for elm in end_loop_mod:
            if not re.search(r'comment_quad_markers', elm, flags=re.DOTALL | re.I):
                source_data = source_data.replace(elm, '\nend loop;\n')
    source_data = re.sub(r'\bdbms_output\s*\.\s*enable', '--dbms_output.enable', source_data, flags=re.DOTALL | re.I)
    return source_data


def schema_handling(source_data, schema, conversion_file_path):
    source_data = re.sub(r' +', ' ', source_data)
    source_data = re.sub(r'\bfrom\s*dual\b', '', source_data, flags=re.DOTALL | re.I)
    insert_tables = re.findall(r'\binsert\s*into\s*.*?\S*', source_data, flags=re.DOTALL | re.I)
    update_tables = re.findall(r'\bupdate\b\s*.*?\bset\b', source_data, flags=re.DOTALL | re.I)
    delete_tables = re.findall(r'(?:\bdelete\s*from\b|\bfrom\b)\s*\S*', source_data, flags=re.DOTALL | re.I)
    join_tables = re.findall(r'\bjoin\s*.*?\bon\b', source_data, flags=re.DOTALL | re.I)
    nextval_tables = re.findall(r'\S*\.nextval', source_data, flags=re.DOTALL | re.I)
    tables_data = insert_tables + update_tables + delete_tables + join_tables + nextval_tables
    tables_list = [i.strip() for i in tables_data if i.strip() and not re.search(r"[.\(\);'\"]", i.strip())]
    for table_data in list(set(tables_list)):

        if re.search(r'(?:\bdelete\s*from\b|\binsert\s*into\b)', table_data, flags=re.I | re.DOTALL):
            modified_table_data = table_data.replace(table_data.split()[2], schema + '.' + table_data.split()[2])
        elif re.search(r'nextval', table_data, flags=re.I | re.DOTALL):
            modified_table_data = table_data.replace(table_data.split()[0], schema + '.' + table_data.split()[0])
        else:
            rep_table = table_data.split()[1]
            modified_table_data = re.sub(rf'\b{rep_table}\b', schema + '.' + rep_table, table_data,
                                         flags=re.DOTALL | re.I)  # table_data.replace(re.escape(table_data.split()[1]+' '), schema + '.' + table_data.split()[1])
        source_data = source_data.replace(table_data, modified_table_data)

    type_tables = re.findall(r'\s+\w+\s*\.\s*\w+\s*\%\s*\w+', source_data, flags=re.DOTALL | re.I)
    for type_data in type_tables:
        modified_table_data = type_data.replace(type_data, ' ' + schema.strip() + '.' + type_data.strip())
        source_data = source_data.replace(type_data, modified_table_data)

    sheet_schema_list = list(
        set(pd.read_excel(conversion_file_path, sheet_name='Code_Objects')['Schema_Name'].values.tolist()))
    code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')
    filtered_df = code_objects_df[
        (code_objects_df['Schema_Name'].str.upper() == schema.upper())
    ]

    code_objects_list_total = set(filtered_df['Object_Name'].tolist())
    code_objects_list_total = [i.replace('.', '_') for i in code_objects_list_total]

    reserved_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                      'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP', 'EXCEPTION', 'IF',
                      'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                      'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                      'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                      'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                      'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT', 'RETURN',
                      'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR', 'SUBSTR',
                      'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX', 'AVG',
                      'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                      'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'ON', 'WHEN']

    begin_end_block = re.findall(r'BEGIN.*END', source_data, flags=re.IGNORECASE | re.DOTALL)
    if begin_end_block:
        modified_begin_block = begin_end_block[0]
        code_objects_list1 = re.findall(r'\s+(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list_2 = re.findall(r'\bBEGIN\s*(\S+)\s*\(', modified_begin_block, flags=re.IGNORECASE | re.DOTALL)
        code_objects_list = code_objects_list1 + code_objects_list_2
        code_objects_list = [i.upper() for i in code_objects_list]
        insert_into_alias_list = re.findall(r'\binsert\s*into\s*\S+\s*(\w+)\s*\(', modified_begin_block,
                                            flags=re.IGNORECASE | re.DOTALL)
        variableswith_indexvar = re.findall(r'(\w+)\s*\(\s*\w+\s*\)\s*\.', source_data, flags=re.DOTALL | re.I)
        reserved_words = reserved_words + insert_into_alias_list + variableswith_indexvar
        code_objects_list = [j for j in code_objects_list_total if j in code_objects_list]
        for code_object_data in code_objects_list:
            code_object_split = code_object_data.split('.')
            if len(code_object_split) == 1 and code_object_data.strip() != '=':
                modified_table_data = re.sub(rf'\s{re.escape(code_object_split[0])}\b',
                                             ' ' + schema.strip() + '.' + code_object_split[0].strip(),
                                             code_object_data,
                                             flags=re.DOTALL | re.I)
                modified_table_data = re.sub(rf'\b{re.escape(code_object_split[0])}\b',
                                             ' ' + schema.strip() + '.' + code_object_split[0].strip(),
                                             modified_table_data,
                                             flags=re.DOTALL | re.I)
                modified_begin_block = re.sub(rf'{code_object_data}', modified_table_data, modified_begin_block,
                                              flags=re.DOTALL | re.I)

            elif len(code_object_split) == 2 and code_object_data.strip() != '=':
                if not code_object_split[0].upper().replace('"', '').strip() in sheet_schema_list:
                    modified_table_data = re.sub(rf'\b{re.escape(code_object_data)}\b',
                                                 ' ' + schema.strip() + '.' + code_object_data.strip(),
                                                 code_object_data,
                                                 flags=re.DOTALL | re.I)
                    modified_begin_block = re.sub(rf'{code_object_data}', modified_table_data, modified_begin_block,
                                                  flags=re.DOTALL | re.I)
        source_data = source_data.replace(begin_end_block[0], modified_begin_block)

    for reserve in reserved_words:
        source_data = re.sub(rf'{schema}\s*\.\s*{reserve}\s*\(', reserve.strip() + '(', source_data,
                             flags=re.DOTALL | re.I)

    schema_unexpected_xml_sequence = re.findall(rf'\b{schema}\s*\.\w+\s*\.\w+\s*\(', source_data,
                                                flags=re.DOTALL | re.I)
    for xml_sequence in schema_unexpected_xml_sequence:
        xml_modified = re.sub(rf'\b{schema}\s*\.\b', '', xml_sequence, flags=re.DOTALL | re.I)
        source_data = source_data.replace(xml_sequence, xml_modified)
    source_data = re.sub(rf'\b{schema}\s*.\s*\(', ' (', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\)\s*{schema}\b\s*.', ') ', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*.\s*=', ' =', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'=\s*{schema}\b\s*.', ' =', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*.{schema}\b', schema + '.', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*\.\s*set\b', ' Set ', source_data, flags=re.DOTALL | re.I)
    schema_name_var_equals = re.findall(rf'\b{schema}\s*\.\w+\s*\=', source_data, flags=re.DOTALL | re.I)
    for schema_equals in schema_name_var_equals:
        schema_equals_mod = re.sub(rf'\b{schema}\s*\.\b', '', schema_equals, flags=re.DOTALL | re.I)
        source_data = source_data.replace(schema_equals, schema_equals_mod)
    double_schema_name = re.findall(rf'\b{schema}\s*\.\w+\s*{schema}\s*\.\w+', source_data, flags=re.DOTALL | re.I)
    for double in double_schema_name:
        double1 = re.sub(rf'\b{schema}\s*\.', '', double, flags=re.DOTALL | re.I)
        double1 = schema + '.' + double1.strip() + ' '
        source_data = source_data.replace(double, double1)

    limit_not = re.findall(r'\brownum\s*\<\s*\>\s*\d+', source_data, flags=re.DOTALL | re.I)
    for limit in limit_not:
        limit1 = re.sub(r'\<\s*\>', '<', limit, flags=re.DOTALL | re.I)
        source_data = source_data.replace(limit, limit1)
    source_data = re.sub(rf'\b{schema}\s*\.\s*\.\b', schema.strip() + '.', source_data, flags=re.DOTALL | re.I)
    source_data = re.sub(rf'\b{schema}\s*\.\s*{schema}\s*\.', schema.strip() + '.', source_data, flags=re.DOTALL | re.I)
    return source_data


def cast_handling(source_data, schema, object_name, conversion_file_path):
    source_data = re.sub(r' +', ' ', source_data)

    pre_defined_words = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'MERGE', 'GRANT',
                         'REVOKE', 'LISTAGG', 'COMMIT', 'ROLLBACK', 'SAVEPOINT', 'BEGIN', 'END', 'LOOP',
                         'EXCEPTION', 'IF',
                         'THEN', 'ELSIF', 'PROCEDURE', 'FUNCTION', 'PACKAGE', 'TRIGGER', 'FROM', 'WHERE', 'GROUP',
                         'GROUP BY', 'HAVING', 'ORDER BY', 'AND', 'OR', 'NOT', 'IN', 'BETWEEN', 'LIKE', 'IS NULL',
                         'EXISTS', 'DISTINCT', 'ASC', 'DESC', 'NULL', 'TRUE', 'FALSE', 'ROWNUM', 'COLUMN', 'TABLE',
                         'VIEW', 'INDEX', 'SEQUENCE', 'USER', 'SYSDATE', 'NVL', 'TO_CHAR', 'TO_DATE', 'TO_NUMBER',
                         'CAST', 'COALESCE', 'TRUNC', 'DATE_TRUNC', 'BY', 'VALUES', 'DUAL', 'NVL', 'COUNT',
                         'RETURN',
                         'RETURNS', 'DBMS_OUTPUT.PUT_LINE', 'VARCHAR2', 'WHILE', 'NVARCHAR2', 'CHAR', 'INSTR',
                         'SUBSTR',
                         'EXTRACT', 'XMLTYPE', 'SUM', 'UPPER', 'LOWER', 'TRIM', 'LTRIM', 'RTRIM', 'JOIN', 'MAX',
                         'AVG',
                         'ROUND', 'DECODE', 'MIN', 'ELSE', '.GETSTRINGVAL', '.GETNUMBERVAL', 'GETSTRINGVAL',
                         'GETNUMBERVAL', 'RAISE', 'EXTRACTVALUE', 'REPLACE', 'CASE', 'UPPER', 'LOWER']

    from_block_list = re.findall(r'\bfrom\b.*?;', source_data, flags=re.IGNORECASE | re.DOTALL)
    for from_block in from_block_list:
        modified_from_block = from_block
        compare_words = re.findall(
            r'(?:trunc\s*\(\s*\S+\s*\)|\w+\s*\.\s*\w+|\w+)?\s*=\s*(?:trunc\s*\(\s*\S+\s*\)|\w+\s*\.\s*\w+|\w+)?',
            from_block, flags=re.DOTALL | re.I)

        filtered_compare_words = [
            word for word in compare_words
            if not any(pre_word in set(re.findall(r'\b\w+\b', word, flags=re.IGNORECASE | re.DOTALL)) for pre_word in
                       pre_defined_words)
        ]

        alias_names = list({re.findall(r'(\w+)\s*\.', i, flags=re.DOTALL | re.I)[0] for i in compare_words if
                            re.findall(r'(\w+)\s*\.', i, flags=re.DOTALL | re.I)})

        alias_table_names_data = [re.findall(rf'\S+\s*{i}\s+', from_block, flags=re.IGNORECASE | re.DOTALL) for i in
                                  alias_names]
        filtered_alias_table_names = [
            word.replace('"', '').upper().strip() for sublist in alias_table_names_data
            for word in sublist
            if not re.search(r'\b(?:OR|=|AND|ON)\b|=\s*\w+\s+', word) and '(' not in word and len(word.split()) == 2
        ]
        tables = re.findall(rf'from\s*(\S+)\s*', from_block, flags=re.IGNORECASE | re.DOTALL)

        table_df = pd.read_excel(conversion_file_path, sheet_name='Table')
        table_df['Combined_Column'] = table_df['Schema_Name'] + '.' + table_df['Object_Name']
        filtered_table_df = table_df[table_df['Combined_Column'].isin(
            [i.split()[0].strip() for i in set(filtered_alias_table_names + tables)])] if not table_df[
            table_df['Combined_Column'].isin([i.split()[0].strip() for i in filtered_alias_table_names])].empty else \
            table_df[['Combined_Column', 'Input_Variable', 'Data_Type']].drop_duplicates()

        all_tables = [i.split()[0].strip() for i in set(filtered_alias_table_names + tables)]

        code_objects_df = pd.read_excel(conversion_file_path, sheet_name='Code_Objects')

        code_objects_df['Combined_Column'] = code_objects_df['Schema_Name'] + '.' + code_objects_df['Object_Name']
        filtered_object_df = \
            code_objects_df[code_objects_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper().strip()}'][
                ['Combined_Column', 'Input_Variable', 'Data_Type']].drop_duplicates()
        first_variable_datatype = pd.DataFrame()
        second_variable_datatype = pd.DataFrame()
        filtered_compare_words = [i for i in filtered_compare_words if
                                  not re.search(r'\=\s*\d+', i, flags=re.DOTALL | re.I)]

        for compare_sentence in list(set(filtered_compare_words)):
            modified_sentence = compare_sentence

            first_word = modified_sentence.split('=')[0].strip()
            second_word = modified_sentence.split('=')[1].strip()

            if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                if '.' in first_word:
                    first_alias_name = first_word.split('.')[0].strip().upper()
                    first_variable_name = first_word.split('.')[1].strip().upper()
                    first_table_name = next(
                        (item.split()[0] for item in filtered_alias_table_names if
                         item.split()[1].strip().upper() == first_alias_name),
                        None
                    )
                    first_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == first_table_name) &
                        (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                else:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
            elif '.' in first_word:
                first_alias_name = first_word.split('.')[0].strip().upper()
                first_variable_name = first_word.split('.')[1].strip().upper()
                first_table_name = next(
                    (item.split()[0] for item in filtered_alias_table_names if
                     item.split()[1].strip().upper() == first_alias_name),
                    None
                )
                first_variable_datatype = filtered_table_df.loc[
                    (filtered_table_df['Combined_Column'] == first_table_name) &
                    (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
            else:

                object_condition = filtered_object_df[filtered_object_df['Input_Variable'] == first_word.upper()]

                table_condition = filtered_table_df[
                    (filtered_table_df['Input_Variable'] == first_word.upper()) &
                    (filtered_table_df['Combined_Column'].isin(all_tables))
                    ]
                if not table_condition.empty:
                    first_variable_datatype = \
                        filtered_table_df[filtered_table_df['Input_Variable'] == first_word.upper()][
                            'Data_Type']

                if not object_condition.empty:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df[
                             'Combined_Column'] == f'{schema.upper().strip()}.{object_name.upper().strip()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type'
                    ]
            if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                if '.' in second_word:
                    second_alias_name = second_word.split('.')[0].strip().upper()
                    second_variable_name = first_word.split('.')[1].strip().upper()
                    second_table_name = next(
                        (item.split()[0] for item in all_tables if
                         item.split()[1].strip().upper() == second_alias_name),
                        None
                    )
                    second_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == second_table_name) &
                        (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                else:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
            elif '.' in second_word:
                second_alias_name = second_word.split('.')[0].strip().upper()
                second_variable_name = second_word.split('.')[1].strip().upper()
                second_table_name = next(
                    (item.split()[0] for item in filtered_alias_table_names if
                     item.split()[1].strip().upper() == second_alias_name),
                    None
                )
                second_variable_datatype = filtered_table_df.loc[
                    (filtered_table_df['Combined_Column'] == second_table_name) &
                    (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
            else:

                object_condition = filtered_object_df[filtered_object_df['Input_Variable'] == second_word.upper()]

                table_condition = filtered_table_df[
                    (filtered_table_df['Input_Variable'] == second_word.upper()) &
                    (filtered_table_df['Combined_Column'].isin(all_tables))
                    ]
                if not table_condition.empty:
                    second_variable_datatype = \
                        filtered_table_df[filtered_table_df['Input_Variable'] == second_word.upper()][
                            'Data_Type']

                if not object_condition.empty:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df[
                             'Combined_Column'] == f'{schema.upper().strip()}.{object_name.upper().strip()}') &
                        (filtered_object_df['Input_Variable'] == second_word.upper()), 'Data_Type'
                    ]

            first_variable_datatype = first_variable_datatype.reset_index(drop=True)
            second_variable_datatype = second_variable_datatype.reset_index(drop=True)

            if re.search(r'\btrunc\s*\(', first_word, flags=re.IGNORECASE | re.DOTALL) or re.search(r'\btrunc\s*\(',
                                                                                                    second_word,
                                                                                                    flags=re.IGNORECASE | re.DOTALL):
                if not first_variable_datatype.empty:
                    if str(first_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = first_word + ' = ' + second_word
                        modified_sentence = modified_sentence.replace(first_word,
                                                                      first_word.replace('trunc', '').replace('(',
                                                                                                              '').replace(
                                                                          ')', '') + '::date')
                if not second_variable_datatype.empty:
                    if str(second_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = first_word + ' = ' + second_word
                        modified_sentence = modified_sentence.replace(second_word,
                                                                      second_word.replace('trunc', '').replace('(',
                                                                                                               '').replace(
                                                                          ')', '') + '::date')
            elif not first_variable_datatype.empty and not second_variable_datatype.empty:
                if (first_variable_datatype.values[0] != second_variable_datatype.values[0]):
                    if str(first_variable_datatype.values[0]).lower() == 'number' and str(
                            second_variable_datatype.values[0]).lower() != 'number':
                        modified_sentence = first_word + ' = ' + second_word + '::numeric'
                    elif str(second_variable_datatype.values[0]).lower() == 'number' and str(
                            first_variable_datatype.values[0]).lower() != 'number':
                        modified_sentence = first_word + '::numeric = ' + second_word

            modified_from_block = modified_from_block.replace(compare_sentence, modified_sentence)

        difference_data = re.findall(r'(?:\w+\.\w+|\w+)\s*(?:\+|\-)\s*(?:\w+\.\w+|\w+)', from_block,
                                     flags=re.DOTALL | re.I)

        for compare_sentence in difference_data:
            if not re.search(r'\d+', compare_sentence, flags=re.DOTALL):
                modified_sentence = compare_sentence

                first_word = re.split(r'(?:\+|\-)', modified_sentence, flags=re.DOTALL | re.I)[
                    0].strip()  # .split('=')[0].strip()
                second_word = re.split(r'(?:\+|\-)', modified_sentence, flags=re.DOTALL | re.I)[1].strip()

                if re.search(r'\btrunc\b', first_word, flags=re.IGNORECASE | re.DOTALL):
                    if '.' in first_word:
                        first_alias_name = first_word.split('.')[0].strip().upper()
                        first_variable_name = first_word.split('.')[1].strip().upper()
                        first_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == first_alias_name),
                            None
                        )
                        first_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == first_table_name) &
                            (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                    else:
                        first_variable_datatype = filtered_object_df.loc[
                            (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                            (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                elif '.' in first_word:
                    first_alias_name = first_word.split('.')[0].strip().upper()
                    first_variable_name = first_word.split('.')[1].strip().upper()
                    first_table_name = next(
                        (item.split()[0] for item in filtered_alias_table_names if
                         item.split()[1].strip().upper() == first_alias_name),
                        None
                    )
                    first_variable_datatype = filtered_table_df.loc[
                        (filtered_table_df['Combined_Column'] == first_table_name) &
                        (filtered_table_df['Input_Variable'] == first_variable_name), 'Data_Type']
                else:
                    first_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']

                if re.search(r'\btrunc\b', second_word, flags=re.IGNORECASE | re.DOTALL):
                    if '.' in second_word:
                        second_alias_name = second_word.split('.')[0].strip().upper()
                        second_variable_name = first_word.split('.')[1].strip().upper()
                        second_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == second_alias_name),
                            None
                        )
                        second_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == second_table_name) &
                            (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                    else:
                        second_variable_datatype = filtered_object_df.loc[
                            (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                            (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                elif '.' in second_word:
                    if len(first_word.split('.')) == 2:
                        second_alias_name = second_word.split('.')[0].strip().upper()
                        second_variable_name = first_word.split('.')[1].strip().upper()
                        second_table_name = next(
                            (item.split()[0] for item in filtered_alias_table_names if
                             item.split()[1].strip().upper() == second_alias_name),
                            None
                        )
                        second_variable_datatype = filtered_table_df.loc[
                            (filtered_table_df['Combined_Column'] == second_table_name) &
                            (filtered_table_df['Input_Variable'] == second_variable_name), 'Data_Type']
                else:
                    second_variable_datatype = filtered_object_df.loc[
                        (filtered_object_df['Combined_Column'] == f'{schema.upper()}.{object_name.upper()}') &
                        (filtered_object_df['Input_Variable'] == first_word.upper()), 'Data_Type']
                if not first_variable_datatype.empty and not second_variable_datatype.empty:
                    if str(first_variable_datatype.values[0]).lower() == 'date' and str(
                            second_variable_datatype.values[0]).lower() == 'date':
                        modified_sentence = 'EXTRACT(EPOCH FROM(' + compare_sentence.strip() + '))/(24*60*60)'

                modified_from_block = modified_from_block.replace(compare_sentence, modified_sentence)

        source_data = source_data.replace(from_block, modified_from_block)

    source_data = re.sub(r'to_date\s+\(', 'to_date(', source_data, flags=re.I)
    to_date_data = re.findall(r"to_date\s*\(.*?\)\s*", source_data, flags=re.IGNORECASE | re.DOTALL)
    for compare_sentence in to_date_data:
        remaining_statement_part = source_data.split(compare_sentence.strip())
        if len(remaining_statement_part) > 1:
            remaining_statement_part = source_data.split(compare_sentence.strip())[1]
            remaining_bracket_level = 0
            remaining_bracket_level_list = []
            remaining_statement = ''
            for c in compare_sentence + remaining_statement_part:
                if c == "(":
                    remaining_bracket_level += 1
                elif c == ")":
                    remaining_bracket_level -= 1
                remaining_statement = remaining_statement + c
                remaining_bracket_level_list.append(remaining_bracket_level)
                if 1 in remaining_bracket_level_list and remaining_bracket_level == 0:
                    break
                else:
                    continue
            modified_statement = remaining_statement
            parts = []
            bracket_level = 0
            current = []
            for c in (remaining_statement[8:-1] + ","):
                if c == "," and bracket_level == 0:
                    parts.append("".join(current))
                    current = []
                else:
                    if c == "(":
                        bracket_level += 1
                    elif c == ")":
                        bracket_level -= 1
                    current.append(c)
            parts = [i for i in parts if i != '']
            if len(parts) == 2:
                first_word = parts[0]
                second_word = parts[1]

                if (
                        'dd' in second_word.lower() or 'mm' in second_word.lower() or 'yy' in second_word.lower()) and 'to_' not in first_word.lower():
                    modified_statement = f'to_date(to_char({first_word},{second_word}))'

            source_data = source_data.replace(remaining_statement, modified_statement)
    operator_type1 = re.findall(r'(?:\>|\<|\=|\,|\!)\s*\:\:numeric', source_data, flags=re.DOTALL | re.I)
    operator_type2 = re.findall(r'(?:\>|\<|\=|\,|\!)\s*\:numeric', source_data, flags=re.DOTALL | re.I)
    operator_type = operator_type1 + operator_type2
    for operator in operator_type:
        operator_mod = re.sub(rf'::numeric', '', operator, flags=re.DOTALL | re.I)
        source_data = source_data.replace(operator, operator_mod)
    for i in pre_defined_words:
        source_data = re.sub(rf'\b{i}\s*\:\:numeric', i, source_data, flags=re.DOTALL | re.I)
    return source_data

def decrypt_conversion_file(file_path, file_encryption_key):
    """
    Decrypt conversion file and return the decrypted content as string.

    Args:
        file_path: Path to the encrypted file
        file_encryption_key: Encryption key for decryption

    Returns:
        str: Decrypted Python code as string
    """
    f = Fernet(file_encryption_key)
    with open(file_path, 'rb') as encrypted_file:
        encrypted = encrypted_file.read()
    decrypted = f.decrypt(encrypted)
    return decrypted.decode('utf-8')



def load_module_from_encrypted_file(file_path, decrypt_key, module_name):
    """
    Load a Python module from an encrypted file using importlib.

    Args:
        file_path: Path to the encrypted .py file
        decrypt_key: Decryption key
        module_name: Name of the module to create

    Returns:
        module: Loaded Python module object
    """
    # Decrypt the file content
    decrypted_code = decrypt_conversion_file(file_path, decrypt_key)

    # Create module from source code using importlib
    spec = importlib.util.spec_from_loader(module_name, loader=None)
    module = importlib.util.module_from_spec(spec)

    # Execute the decrypted code in the module's namespace
    exec(decrypted_code, module.__dict__)

    return module


def feature_execution_and(source_data, schema, folder_path, value, decrypt_key):
    applied_features = []

    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    if '&' in str(value.iloc[2]):
        excel_list_all = str(value.iloc[2]).split('&')
        list_all = []
        for key in excel_list_all:
            if '~' in key:
                est = re.findall(r'~\S+', key)[0]
                key = key.replace(est, '').strip()
                list_all.append(key)
            else:
                key = key.strip()
                list_all.append(key)
        list_all = [i for i in list_all if i != '']
        status_list = []
        for key in list_all:
            if '*' in key or '%' in key or '.' in key:
                key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace(
                    '..',
                    '.').replace(
                    '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
                key = key.replace('(', r'\(').replace('%', '.*?').replace(')', r'\)').replace('.', r'\.').replace(r'\.*?',
                                                                                                               '.*?')
                key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
            else:
                ignore_list = [':=', ':']
                check_ignore_item = ['True' for i in ignore_list if i in key]
                if check_ignore_item:
                    key_pattern = r'\s*' + re.escape(key) + r'\s*'
                else:
                    key_pattern = r'\b' + re.escape(key) + r'\b'
                key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
                status_list.append(len(key_pattern_list))
        if 0 not in status_list:
            try:
                encrypt_decrypt_path = folder_path + value.iloc[6] + '/' + str(value.iloc[1]).lower() + '.py'
                module_name = str(value.iloc[1]).lower()
                # Load module directly from encrypted file using importlib
                import_object = load_module_from_encrypted_file(encrypt_decrypt_path, decrypt_key, module_name)
                # print('function_name:', function_name)
                function_name = [i for i in dir(import_object) if i.lower() == str(value.iloc[1]).lower()][0]
                function_call = getattr(import_object, function_name.strip())
                applied_features.append((function_name, value.iloc[6] + '/' + str(value.iloc[1]).lower() + '.py'))
                # print(source_data, 'source_data before function call')
                output = function_call(source_data, schema)
                # print(output, 'output after function call')
                source_data = output
            except Exception as e:
                print('Error in conversion ' + str(e) + ' at module ' + str(value.iloc[1]).lower())
                source_data = source_data
    return source_data, applied_features


def feature_execution_pipe(source_data, schema, folder_path, value, decrypt_key):
    applied_features = []
    source_data = source_data.replace(' ( ', '(').replace(' ) ', ')').replace(' (', '(').replace('( ', '(').replace(
        ' )', ')').replace(') ', ')')
    excel_list_any = str(value.iloc[2]).split('|')
    list_any = []
    for key in excel_list_any:
        if '~' in key:
            est = re.findall(r'~\S+', key)[0]
            key = key.replace(est, '').strip()
            list_any.append(key)
        else:
            key = key.strip()
            list_any.append(key)
    list_any = [i for i in list_any if i != '']
    status_list = []
    for key in list_any:
        key = key.strip()
        if '*' in key or '%' in key or '.' in key:
            key = key.replace(' * ', '.*?').replace(' *', '.*?').replace('* ', '.*?').replace('*', '.*?').replace('..',
                                                                                                                  '.').replace(
                '??', '?').replace(' (', '(').replace('( ', '(').replace(' )', ')').replace(') ', ')')
            key = key.replace('(', r'\(').replace('%', '.*?').replace(')', r'\)').replace('.', r'\.').replace(r'\.*?',
                                                                                                           '.*?')
            key_pattern_list = re.findall(rf'{key}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
        else:
            ignore_list = [':=', ':']
            check_ignore_item = ['True' for i in ignore_list if i in key]
            if check_ignore_item:
                key_pattern = r'\s*' + re.escape(key) + r'\s*'
            else:
                key_pattern = r'\b' + re.escape(key) + r'\b'
            key_pattern_list = re.findall(rf'{key_pattern}', source_data, re.DOTALL | re.IGNORECASE)
            status_list.append(len(key_pattern_list))
    if any(i > 0 for i in status_list):
        try:
            encrypt_decrypt_path = folder_path + value.iloc[6] + '/' + str(value.iloc[1]).lower() + '.py'
            module_name = str(value.iloc[1]).lower()
            # Load module directly from encrypted file using importlib
            import_object = load_module_from_encrypted_file(encrypt_decrypt_path, decrypt_key, module_name)
            function_name = [i for i in dir(import_object) if i.lower() == str(value.iloc[1]).lower()][0]
            # print('function_name:', function_name)
            function_call = getattr(import_object, function_name.strip())
            applied_features.append((function_name, value.iloc[6] + '/' + str(value.iloc[1]).lower() + '.py'))
            # print(source_data, 'source_data before function call')
            output = function_call(source_data, schema)
            # print(output, 'output after function call')
            source_data = output
        except Exception as e:
            print('Error in conversion ' + str(e) + ' at module ' + str(value.iloc[1]).lower())
    return source_data, applied_features


def feature_execution(source_data, schema, folder_path, excel_data, complete_excel_data, decrypt_key):
    feature_execution_list = []
    applied_features_list = []
    for _, value in excel_data.iterrows():
        if value.iloc[3] == 'No Predecessor':
            if value.iloc[1] not in feature_execution_list:
                if '&' in str(value.iloc[2]):
                    source_data, applied_features = feature_execution_and(source_data, schema, folder_path, value, decrypt_key)
                else:
                    source_data, applied_features = feature_execution_pipe(source_data, schema, folder_path, value, decrypt_key)
                feature_execution_list.append(value.iloc[1])
            else:
                source_data = source_data
                feature_execution_list = feature_execution_list
                applied_features = []
        else:
            if value.iloc[3] in feature_execution_list:
                if '&' in str(value.iloc[2]):
                    source_data, applied_features = feature_execution_and(source_data, schema, folder_path, value, decrypt_key)
                else:
                    source_data, applied_features = feature_execution_pipe(source_data, schema, folder_path, value, decrypt_key)
                feature_execution_list.append(value.iloc[1])
            else:
                parent_feature_name = value.iloc[3]
                pred_excel_data = complete_excel_data[complete_excel_data['Feature_Name'] == parent_feature_name]
                source_data, applied_features = feature_execution(source_data, schema, folder_path, pred_excel_data, complete_excel_data, decrypt_key)
                if '&' in str(value.iloc[2]):
                    source_data, applied_features = feature_execution_and(source_data, schema, folder_path, value, decrypt_key)
                else:
                    source_data, applied_features = feature_execution_pipe(source_data, schema, folder_path, value, decrypt_key)
                feature_execution_list.append(value.iloc[3])
                feature_execution_list.append(value.iloc[1])
        applied_features_list.extend(applied_features)
    return source_data, applied_features_list


def build_common_object_list(objects_excel_data, object_path):
    object_path_list = []
    object_excel_data = objects_excel_data[objects_excel_data['Object_Id'] == object_path]
    object_path_list.append(object_path)
    if not object_excel_data.empty:
        if type(object_excel_data.iloc[0]['Linked_Objects']) is not float and not isinstance(
                object_excel_data.iloc[0]['Linked_Objects'], np.float64):
            linked_objects_split = object_excel_data.iloc[0]['Linked_Objects'].split(',')
            if linked_objects_split:
                for path in linked_objects_split:
                    temp_list = build_common_object_list(objects_excel_data, path)
                    if temp_list:
                        object_path_list.extend(temp_list)
    return object_path_list


def tuple_execution(object_tuple, schema_name, object_path, modules_data, rules_data, objects_data, scripts_path, decrypt_key):
    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Pre'))
    pre_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]
    
    pre_output = object_tuple[1]
    pre_applied_features_list = []
    csv_list = []
    if not pre_module_data.empty:
        pre_output, pre_applied_features_list  = feature_execution(object_tuple[1], schema_name, scripts_path, pre_module_data, pre_module_data, decrypt_key)

    pre_output = re.sub(r'\bas\b', '\nas', pre_output, flags=re.I)
    pre_output = re.sub(r'\bis\b', '\nis', pre_output, flags=re.I)
    
    pre_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in pre_output.split('\n') if i.strip()])
    current_object_data = objects_data[(objects_data['Object_Id'] == object_path)]
    if current_object_data.iloc[0]['Object_Process_Style'] == 'Sequential':
        sub_object_path = object_path + '/'
        sub_objects_data = objects_data[
            objects_data['Object_Id'].str.contains(object_path) & ~objects_data['Object_Id'].str.contains(
                'Pre|Post')].sort_values("Object_Execution_Order")

        sub_object_names_list = []
        object_path_length = len([i for i in sub_object_path.split('/') if i])
        sub_object_names_list.extend(row['Object_Id'].rsplit('/', 1)[-1] for _, row in sub_objects_data.iterrows() if
                                     len(row['Object_Id'].split('/')) == object_path_length + 1)
        for sub_object in sub_object_names_list:
            current_object_path = object_path + '/' + sub_object
            sub_objects_list = create_sub_objects(pre_output, current_object_path, rules_data, objects_data)
            # print(sub_objects_list, 'sub_objects_list before processing')
            counter = 0
            for index, sub_tuple in enumerate(sub_objects_list, 1):
                sub_tuple_data = '\n'.join(
                    [re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])
                print("after type casting and pre:", sub_tuple_data, '==', index)
                so_path_applied_list, sub_object_output = [], ''
                # print("sub_tuple_data:============", sub_tuple_data.strip().lower())
                # print(pre_output.lower().strip(), '=======================')
                if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                    pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                        f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                        flags=re.DOTALL | re.I)
                    sub_object_output, so_path_applied_list, sub_csv_list = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                        rules_data, objects_data, scripts_path, decrypt_key)
                    print("after statement level conversion:", sub_object_output)
                    csv_list.extend(sub_csv_list)
                    pre_output = str(pre_output).replace(
                        ' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                        '\n' + str(sub_object_output))
                else:
                    pre_output = pre_output
                counter = counter + 1

                # Collect CSV data for all statement-level processing
                if current_object_path in ['Procedure/Statement', 'Function/Statement']:
                    csv_tuple = (index, sub_tuple_data, sub_object_output, so_path_applied_list)
                    csv_list.append(csv_tuple)

    elif current_object_data.iloc[0]['Object_Process_Style'] == 'Mutually Exclusive':
        sub_object_path = object_path + '/'
        sub_objects_list = create_sub_objects(pre_output, sub_object_path, rules_data, objects_data)

        counter = 0
        for index, sub_tuple in enumerate(sub_objects_list, 1):
            sub_tuple_data = '\n'.join([re.sub(r' +', ' ', i).strip() for i in sub_tuple[1].split('\n') if i.strip()])

            so_path_applied_list, sub_object_output = [], ''
            if sub_tuple_data.strip().lower() in pre_output.lower().strip():
                pre_output = re.sub(rf'{re.escape(sub_tuple_data.strip())}',
                                    f' qbook_replace_{sub_tuple[0]}_{counter}_us ', pre_output, 1,
                                    flags=re.DOTALL | re.I)
                current_object_path = object_path + '/' + str(sub_tuple[0])
                sub_object_output, so_path_applied_list, sub_csv_list = tuple_execution(sub_tuple, schema_name, current_object_path, modules_data,
                                                    rules_data, objects_data, scripts_path, decrypt_key)
                # Accumulate CSV data from recursive calls
                csv_list.extend(sub_csv_list)
                pre_output = pre_output.replace(' qbook_replace_' + str(sub_tuple[0]) + '_' + str(counter) + '_us ',
                                                '\n' + str(sub_object_output))
            else:
                pre_output = pre_output

            # Collect CSV data for all statement-level processing in Mutually Exclusive style
            if current_object_path in ['Procedure/Statement', 'Function/Statement']:
                csv_tuple = (index, sub_tuple_data, sub_object_output, so_path_applied_list)
                csv_list.append(csv_tuple)

            counter = counter + 1
    else:
        pre_output = pre_output

    object_path_list = build_common_object_list(objects_data, str(object_path + '/' + 'Post'))
    post_module_data = modules_data[modules_data['Object_Path'].isin(object_path_list)]

    post_output, post_applied_features_list = feature_execution(pre_output, schema_name, scripts_path, post_module_data, post_module_data, decrypt_key)
    post_output = '\n'.join([re.sub(r' +', ' ', i).strip() for i in post_output.split('\n') if i.strip()])

    if csv_list:
        csv_list = [t + (post_applied_features_list,) for t in csv_list]

    path_applied_list = []
    path_applied_list.extend(pre_applied_features_list)
    path_applied_list.extend(post_applied_features_list)
    return post_output, path_applied_list, csv_list


def qbook_object_conversion(migration_name, schema_name, object_type, object_name, source_data, cloud_category):

    # Determine QBook path based on cloud_category (for QBook files)
    if cloud_category.lower() == "local":
        qbook_root_path = Config.Qbook_Local_Path
        print(f"🏠 Using QBook Local path: {qbook_root_path}")
    else:  # Cloud
        qbook_root_path = Config.Qbook_Path
        print(f"☁️ Using QBook Cloud path: {qbook_root_path}")

    # No longer need working directory for temporary files since we use in-memory execution
    decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='
    conversion_modules_path = qbook_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '.csv'
    modules_data = pd.read_csv(conversion_modules_path)

    scripts_path = qbook_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/'
    sys.path.append(scripts_path)

    dynamic_rules_path = qbook_root_path + '/' + 'Dynamic_Rules' + '/' + migration_name + '/' + migration_name + '.csv'
    rules_data = pd.read_csv(dynamic_rules_path)

    objects_excel = qbook_root_path + '/' + 'Conversion_Modules' + '/' + migration_name + '/' + migration_name + '_objects_data.csv'
    objects_data = pd.read_csv(objects_excel)

    conversion_file_path = qbook_root_path + '/' + 'Stage1_Metadata' + '/' + migration_name + '/' + schema_name + '/' + 'Conversion_Files.xlsx'
    # Search for files ending with '_Package_Extraction.csv' in the directory
    packages_dir = qbook_root_path + '/' + 'Stage1_Metadata' + '/' + migration_name + '/' + schema_name + '/'
    packages_pattern = packages_dir + '*_Package_Extraction.csv'
    packages_files = glob.glob(packages_pattern)

    column_data_packages_list = []
    if packages_files:
        packages_file_path = packages_files[0]  # Use the first match
        print(f"📦 Found package file: {os.path.basename(packages_file_path)}")
        df = pd.read_csv(packages_file_path)
        if not df.empty and len(df.columns) > 0:
            column_data_packages_list = df['Object_Name'].tolist()
    else:
        print(f"⚠️ No files ending with '_Package_Extraction.csv' found in: {packages_dir}")
        packages_file_path = None

    if object_type == 'Package_Procedure':
        rules_object_name = 'Procedure'
    elif object_type == 'Package_Function':
        rules_object_name = 'Function'
    else:
        rules_object_name = object_type

    object_rules_data = rules_data[(rules_data['Migration_Name'] == migration_name) & (
            rules_data['Object_Path'] == rules_object_name)]

    if not object_rules_data.empty:
        comment_identifiers = \
        rules_data[rules_data['Object_Path'] == object_type]['Comment_Identifiers'].values.tolist()[0]

        source_data = "".join([s for s in source_data.strip().splitlines(True) if s.strip()])
        source_data = re.sub(' +', ' ', source_data)

        comments_data_list = re.findall(r'\/\*[\s\S]*?\*\/', source_data)
        for comment_data in comments_data_list:
            modified_comment_data = comment_data.replace('/*', '/*\n', 1).replace('*/', '\n*/', 1)
            source_data = source_data.replace(comment_data, modified_comment_data)

        source_data, comment_dict = retrieve_comments_with_rules(source_data, comment_identifiers)
        # print(comment_dict, 'comment_dict after retrieve_comments_with_rules')
        source_data = re.sub(r' +', ' ', source_data)

        source_data = re.sub(r'CREATE\sProc\b', 'CREATE PROCEDURE', source_data, flags=re.IGNORECASE | re.DOTALL)
        source_data = source_data.replace('&lt ;', '&lt;').replace('&lt;', '<')
        source_data = source_data.replace('&gt ;', '&gt;').replace('&gt;', '>')
        source_data = re.sub(r':\s*=\s*', ':= ', source_data, flags=re.DOTALL | re.IGNORECASE)
        source_data = re.sub(r' +', ' ', source_data)

        if column_data_packages_list:
            for packages in column_data_packages_list:
                packaeges_co = re.findall(rf'\b{packages}\s*\.\s*\w+\(', source_data, flags=re.DOTALL | re.I)
                if packaeges_co:
                    for code in list(set(packaeges_co)):
                        code1 = code.replace('.', '_')
                        split_code = code.split('.')[1]
                        if split_code.strip().lower().startswith('p_'):
                            source_data = source_data.replace(code, 'CALL ' + code1)
                        else:
                            source_data = source_data.replace(code, code1)

        if object_type in ['Procedure', 'Function', 'Package_Procedure', 'Package_Function']:
            source_data = schema_handling(source_data, schema_name, conversion_file_path)
            source_data = call_statement_handling(source_data, conversion_file_path)
            source_data = cast_handling(source_data, schema_name, object_name.split('/')[-1].replace('.sql', ''),
                                        conversion_file_path)
            

        object_tuple = (object_type, source_data)
        # print(source_data, 'source_data after type casting')
        object_converted_output, path_applied_list, csv_list = tuple_execution(object_tuple, schema_name, object_type, modules_data, rules_data,
                                                  objects_data, scripts_path, decrypt_key)
        # print(object_converted_output, 'object_converted_output after tuple execution')
        object_converted_output = replace_comment_markers(object_converted_output, comment_dict)
        # print(object_converted_output, 'object_converted_output after replace comment markers')
        object_converted_output = add_execute_keyword(object_converted_output)

        # Create DataFrame for statement level conversion
        if csv_list and csv_list[-1][1] == '':
            csv_list = csv_list[:-1]  
        available_features_df = pd.DataFrame(csv_list, columns=['Statement_Number', 'Statement_After_Typecasting', 'Statement_Level_Output', 'Available_Features', 'Post_Features'])

        if not available_features_df.empty:
            metadata_dir = os.path.join(qbook_root_path, 'Stage1_Metadata', migration_name, schema_name, object_type, object_name)
            os.makedirs(metadata_dir, exist_ok=True)

            excel_file_path = os.path.join(metadata_dir, 'Available_Features.csv')

            try:
                available_features_df.to_csv(excel_file_path, index=False)
                print(f"📊 Available Features saved to: {excel_file_path}")
            except Exception as e:
                print(f"⚠️ Error saving Available Features file: {str(e)}")
        else:
            print("ℹ️ No available features data to save")

        return object_converted_output, available_features_df, comment_dict

    else:
        print("Don't have any rules for this object type")
        return None, pd.DataFrame(), {}  # Return empty DataFrame and empty comment_dict if no rules

# qbook_object_conversion('Oracle_Postgres14', 'AICCTEST', 'Procedure', 'P_UPDATELOCATIONID', """
#  CREATE OR REPLACE  PROCEDURE "AICCTEST"."P_UPDATELOCATIONID" 
# (IN_FROMVAL NUMBER,
# IN_TOVAL NUMBER)
# AS

# CURSOR C1
# IS
# SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
# WHERE UTC.COLUMN_NAME LIKE 'LOCATIONID'
# AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM);

# /*cursor c2
# is
# SELECT UTC.TABLE_NAME,UTC.COLUMN_NAME FROM USER_TAB_COLS UTC
# WHERE UTC.COLUMN_NAME LIKE '%POSITION%'
# AND UTC.TABLE_NAME NOT IN (SELECT UM.MVIEW_NAME FROM USER_MVIEWS UM
#                            UNION
#                            SELECT uv.view_name FROM user_views uv);*/

# BEGIN

#      FOR REC IN C1
#      LOOP

#          DBMS_OUTPUT.PUT_LINE('UPDATE '||REC.TABLE_NAME||
#          ' SET '||REC.COLUMN_NAME||'='||IN_TOVAL||' WHERE '||
#          REC.COLUMN_NAME||' = '||IN_FROMVAL||';');

#      END LOOP;

#   /*   DBMS_OUTPUT.PUT_LINE('-----------------------UPDATING POSITONID--------------------');

#        FOR REC IN c2
#      LOOP

#          DBMS_OUTPUT.PUT_LINE('UPDATE '||REC.TABLE_NAME||
#          ' SET '||REC.COLUMN_NAME||'='''||IV_toDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||')'||
#          ' WHERE '||
#          REC.COLUMN_NAME||' = '''||IV_FROMDEPT||'-'''||'||hr.f_desigfromposition('||rec.column_name||');');

#      END LOOP;
# */

# END;
# """, "local")