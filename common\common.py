"""
Common utilities shared between DBA, Performance, and Conversion agents.

This module contains utility functions and classes that are used by multiple agents.
"""

import importlib
from typing import Any
from llm_config.config_manager import ConfigManager
from llm import (
    AzureOpenAILLM,
    OpenAILLM,
    AnthropicLLM,
    GroqLLM,
    GeminiLLM,
    OllamaLLM
)


def get_deployment_function(migration_name: str):
    """
    Dynamically import deployToTargetDatabase function based on migration name.

    Args:
        migration_name: Migration name like 'Oracle_Postgres14'

    Returns:
        deployToTargetDatabase function
    """
    module_path = f"Migrations.{migration_name}.db_connection"
    module = importlib.import_module(module_path)
    return module.deployToTargetDatabase


def create_llm(provider: str, config_manager: ConfigManager) -> Any:
    """Create and initialize a Language Model instance based on the specified provider."""
    if provider == "azure_openai":
        return AzureOpenAILLM(config_manager)
    elif provider == "openai":
        return OpenAILLM(config_manager)
    elif provider == "anthropic":
        return AnthropicLLM(config_manager)
    elif provider == "groq":
        return GroqLLM(config_manager)
    elif provider == "gemini":
        return GeminiLLM(config_manager)
    elif provider == "ollama":
        return OllamaLLM(config_manager)
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")