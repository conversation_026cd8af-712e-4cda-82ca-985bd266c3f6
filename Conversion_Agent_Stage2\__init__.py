"""
Stage 2 Conversion Agent Package for QMigrator Module Updates.

This package provides the complete Stage 2 workflow for processing AI corrections
from Stage 1 to update QMigrator Python modules through a workflow with retry mechanism.

Key Components:
    - state: State management for Stage 2 workflow
    - nodes: Processing nodes for Stage 2 operations
    - workflow: LangGraph workflow builder and orchestration
    
Main Classes:
    - Stage2WorkflowState: Workflow state management
    - Stage2ProcessingNodes: Processing node implementations
    - Stage2GraphBuilder: Workflow graph builder
"""

from Conversion_Agent_Stage2.state import Stage2WorkflowState
from Conversion_Agent_Stage2.nodes import Stage2ProcessingNodes
from Conversion_Agent_Stage2.workflow import Stage2GraphBuilder

__all__ = [
    "Stage2WorkflowState",
    "Stage2ProcessingNodes",
    "Stage2GraphBuilder"
]

__version__ = "1.0.0"
