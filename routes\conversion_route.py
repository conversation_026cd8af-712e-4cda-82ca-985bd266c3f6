import uuid
import os
import csv
import shutil
from fastapi import APIRouter, HTTPException, Form, BackgroundTasks

# Import configuration and LLM components
from config import Config
from llm_config.config_manager import ConfigManager
from common.common import create_llm

# Import workflow components
from Conversion_Agent.workflow.graph_builder import GraphBuilder
from common.setup import Pre_SetupManager
from common.database import request_insert, request_update
from common.database import connect_database
from common.database import get_approved_statements_from_db
from common.api import api_authentication, decrypt_database_details
router = APIRouter()


class Stage1MetadataData:
    def __init__(self, migration_name, tgt_object_id, target_schema_name, objectname,
                 approved_statements, source_code, target_code):
        self.migration_name = migration_name
        self.tgt_object_id = tgt_object_id
        self.target_schema_name = target_schema_name
        self.objectname = objectname
        self.approved_statements = approved_statements  # JSON string from frontend
        self.source_code = source_code
        self.target_code = target_code


def create_stage1_metadata_directory_structure(migration_name: str, schema_name: str, object_type: str, object_name: str, cloud_category: str) -> str:
    """
    Create Stage 1 metadata directory structure based on cloud_category.

    Structure: {base_path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/
    - Local: Uses Config.Qbook_Local_Path (Local_Path + '/qbookv2')
    - Cloud: Uses Config.Qbook_Path
    All files for the object will be stored in this directory.

    Returns: Full directory path
    """
    # Determine base path based on cloud_category
    if cloud_category.lower() == "local":
        base_path = Config.Qbook_Local_Path
        print(f"🏠 Using QBook Local path: {base_path}")
    else:  # Cloud
        base_path = Config.Qbook_Path
        print(f"☁️ Using QBook Cloud path: {base_path}")


    # Create directory path: {base_path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/
    directory_path = os.path.join(
        base_path,
        "Stage1_Metadata",
        migration_name,
        schema_name,
        object_type,
        object_name
    )

    # Create directory if it doesn't exist
    os.makedirs(directory_path, exist_ok=True)
    print(f"📁 Created directory: {directory_path}")
    print(f"📂 Structure: {migration_name}/{schema_name}/{object_type}/{object_name}/")

    return directory_path


def save_approved_statements_csv(directory_path: str, migration_name: str, schema_name: str, object_name: str, object_type: str, approved_statements: list) -> str:
    """
    Save approved statements as CSV file with specified columns.

    CSV Columns: migration_name, schema_name, object_name, object_type, tgt_object_id, source_statement_number,
                target_statement_number, original_source_statement, original_target_statement,
                ai_converted_statement, original_deployment_error
    """
    csv_file_path = os.path.join(directory_path, "approved_statements.csv")

    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # Write header
        writer.writerow([
            'migration_name',
            'schema_name',
            'object_name',
            'object_type',
            'tgt_object_id',
            'source_statement_number',
            'target_statement_number',
            'original_source_statement',
            'original_target_statement',
            'ai_converted_statement',
            'original_deployment_error'
        ])

        # Write data rows
        for statement in approved_statements:
            writer.writerow([
                migration_name,
                schema_name,
                object_name,
                object_type,
                statement[0],  # tgt_object_id
                statement[1],  # source_statement_number
                statement[2],  # target_statement_number
                statement[3],  # original_source_statement
                statement[4],  # original_target_statement
                statement[5],  # ai_converted_statement
                statement[6]   # original_deployment_error
            ])

    print(f"💾 Saved approved statements CSV: {csv_file_path}")
    return csv_file_path


def save_source_target_final_files(directory_path: str, source_code: str, target_code: str, final_code: str) -> tuple:
    """
    Save source, target, and final code files.

    Returns: (source_file_path, target_file_path, final_file_path)
    """
    source_file_path = os.path.join(directory_path, "source_code.sql")
    target_file_path = os.path.join(directory_path, "target_code.sql")
    final_file_path = os.path.join(directory_path, "final_code.sql")

    # Save source code
    with open(source_file_path, 'w', encoding='utf-8') as f:
        f.write(source_code)

    # Save target code
    with open(target_file_path, 'w', encoding='utf-8') as f:
        f.write(target_code)

    # Save final code (reviewed and approved)
    with open(final_file_path, 'w', encoding='utf-8') as f:
        f.write(final_code)

    print(f"💾 Saved source code: {source_file_path}")
    print(f"💾 Saved target code: {target_file_path}")
    print(f"💾 Saved final code: {final_file_path}")

    return source_file_path, target_file_path, final_file_path


def copy_project_file(directory_path: str, run_no: str, schema_name: str, cloud_category: str, project_id: int, filename: str) -> str:
    """
    Copy a file from project fileshare to the metadata directory.

    Source path: {project_path}/PRJ{project_id}SRC/{run_no}/Source/{schema_name}/{filename}
    Target path: {directory_path}/{filename}

    Returns: Target file path if successful, None if file not found (no error raised)
    """
    # Determine project base path based on cloud_category (for project files)
    if cloud_category.lower() == "local":
        project_base_path = Config.Local_Path
    else:  # Cloud
        project_base_path = Config.Cloud_Path

    # Construct source file path with dynamic project ID: PRJ{project_id}SRC
    project_folder = f"PRJ{project_id}SRC"
    source_file_path = os.path.join(project_base_path, project_folder, run_no, "Source", schema_name, filename)
    target_file_path = os.path.join(directory_path, filename)

    try:
        if os.path.exists(source_file_path):
            shutil.copy2(source_file_path, target_file_path)
            print(f"📋 Copied {filename} from: {source_file_path}")
            print(f"📋 Copied {filename} to: {target_file_path}")
            return target_file_path
        else:
            print(f"ℹ️ {filename} not found at: {source_file_path} (skipping)")
            return None
    except Exception as e:
        print(f"⚠️ Error copying {filename}: {str(e)} (skipping)")
        return None


def copy_files_by_pattern(directory_path: str, run_no: str, schema_name: str, cloud_category: str, project_id: int, pattern: str) -> list:
    """
    Copy all files matching a pattern from project fileshare to the metadata directory.

    Source path: {project_path}/PRJ{project_id}SRC/{run_no}/Source/{schema_name}/
    Target path: {directory_path}/

    Returns: List of copied file paths
    """
    import glob

    # Determine project base path based on cloud_category (for project files)
    if cloud_category.lower() == "local":
        project_base_path = Config.Local_Path
    else:  # Cloud
        project_base_path = Config.Cloud_Path

    # Construct source directory path with dynamic project ID: PRJ{project_id}SRC
    project_folder = f"PRJ{project_id}SRC"
    source_directory = os.path.join(project_base_path, project_folder, run_no, "Source", schema_name)

    copied_files = []

    try:
        if os.path.exists(source_directory):
            # Find all files matching the pattern
            search_pattern = os.path.join(source_directory, f"*{pattern}")
            matching_files = glob.glob(search_pattern)

            if matching_files:
                for source_file_path in matching_files:
                    filename = os.path.basename(source_file_path)
                    target_file_path = os.path.join(directory_path, filename)

                    try:
                        shutil.copy2(source_file_path, target_file_path)
                        print(f"📊 Copied {filename} from: {source_file_path}")
                        print(f"📊 Copied {filename} to: {target_file_path}")
                        copied_files.append(target_file_path)
                    except Exception as e:
                        print(f"⚠️ Error copying {filename}: {str(e)} (skipping)")
            else:
                print(f"ℹ️ No files ending with '{pattern}' found in: {source_directory} (skipping)")
        else:
            print(f"ℹ️ Source directory not found: {source_directory} (skipping)")

    except Exception as e:
        print(f"⚠️ Error searching for files with pattern '{pattern}': {str(e)} (skipping)")

    return copied_files




class ConversionData:
    def __init__(self, source_code, target_code, deployment_error, max_attempt_per_statement,
                 tgt_object_id, target_connection_id, migration_name, project_id, objectname,
                 cloud_category, run_number, target_schema_name, object_type):
        self.source_code = source_code
        self.target_code = target_code
        self.deployment_error = deployment_error
        self.max_attempt_per_statement = max_attempt_per_statement
        self.tgt_object_id = tgt_object_id
        self.target_connection_id = target_connection_id
        self.migration_name = migration_name
        self.project_id = project_id
        self.objectname = objectname
        self.cloud_category = cloud_category
        self.run_number = run_number
        self.target_schema_name = target_schema_name
        self.object_type = object_type


def run_conversion_background_task(request_data: ConversionData):
    """
    Background task function to run the conversion workflow.

    This function contains the complete conversion logic that runs in the background,
    allowing the API endpoint to return immediately while the conversion process continues.
    """
    try:
        print(f"🚀 Starting background conversion workflow...")

        # Pre-setup migration environment
        setup_manager = Pre_SetupManager(Config, request_data)
        print("🔧 Environment setup completed successfully.")

        # Get database names from environment variables (set by pre_setup_migration)
        source_db = Config.get_source_database()
        target_db = Config.get_target_database()
        print(f"📊 Source DB: {source_db} → Target DB: {target_db}")
        print(f"📁 Conversion files path: {setup_manager.conversion_path}")

        if setup_manager.target_db_credentials:
            print(f"🔐 Target DB credentials Fetched from API")
        else:
            print("⚠️ Target DB credentials not available")
            raise ValueError("Target DB credentials not available")

        if setup_manager.project_DB_details:
            print(f"🔐 Project DB credentials Fetched from API")
        else:
            print("⚠️ Project DB credentials not available")
            raise ValueError("Project DB credentials not available")

        # request insertion into DB
        prj_connection = connect_database(setup_manager.project_DB_details)
        request_id = request_insert(prj_connection, request_data.run_number, request_data.target_connection_id, 'Conversion-Agent','All',
                                    request_data.target_schema_name+'-'+ request_data.objectname, request_data.object_type)[0]

        # Setup the LLM
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider}")

        # Initialize the workflow graph builder with the LLM
        graph_builder = GraphBuilder(llm)
        graph_builder.setup_graph()

        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        # Create a unique thread ID for this workflow execution
        thread_id = f"thread_{uuid.uuid4()}"
        print(f"🔗 Using thread ID: {thread_id}")

        # Execute the complete migration workflow with initial state
        graph_builder.invoke_graph({
            "source_code": request_data.source_code,
            "target_code": request_data.target_code,
            "deployment_error": request_data.deployment_error,
            "conversion_path": setup_manager.conversion_path,
            "target_db_credentials": setup_manager.target_db_credentials,
            "project_db_credentials": setup_manager.project_DB_details,
            "iteration_count": 1,
            "max_attempt_per_statement": request_data.max_attempt_per_statement,
            "migration_name": request_data.migration_name,
            "target_object_id": request_data.tgt_object_id,
            "object_type": request_data.object_type,
        }, thread_id=thread_id)

        print("🎉 Conversion workflow completed!")

        # Update the request status in the database
        request_update(prj_connection, request_id, 'Completed', None)

    except Exception as e:
        print(f"❌ Background conversion workflow failed: {str(e)}")
        # You might want to update the database with error status here
        # request_update(prj_connection, request_id, 'Failed', str(e))



@router.post("/conversion-agent")
def conversion_agent(
    background_tasks: BackgroundTasks,
    source_code: str = Form(..., description="Original source database code"),
    target_code: str = Form(..., description="Target database code with deployment errors"),
    deployment_error: str = Form(..., description="Error message from target database deployment attempt"),
    max_attempt_per_statement: int = Form(default=5, description="Maximum attempts per target statement"),
    tgt_object_id: int = Form(..., description="Target object ID for the migration"),
    target_connection_id: int = Form(..., description="Target database connection ID for the migration"),
    migration_name: str = Form(..., description="Name of the migration"),
    project_id: int = Form(..., description="Project ID for the migration"),
    objectname: str = Form(..., description="Name of the object being migrated"),
    cloud_category: str = Form(..., description="Cloud category for the migration"),
    run_number: int = Form(..., description="Run number for the migration"),
    target_schema_name: str = Form(..., description="Target schema name for the migration"),
    object_type: str = Form(..., description="Type of the object being migrated")
):
    """
    Convert database code using AI-driven migration workflow.

    This endpoint accepts form data instead of JSON, making it easier to handle
    large SQL content with control characters (newlines, tabs, etc.) without
    JSON encoding issues.

    The conversion process runs in the background, allowing this endpoint to return
    immediately while the conversion continues. Check the logs to track progress.
    """

    try:
        print(f"🚀 Starting conversion workflow in background...")

        # Create request object from form data
        request_data = ConversionData(
            source_code=source_code,
            target_code=target_code,
            deployment_error=deployment_error,
            max_attempt_per_statement=max_attempt_per_statement,
            tgt_object_id=tgt_object_id,
            target_connection_id=target_connection_id,
            migration_name=migration_name,
            project_id=project_id,
            objectname=objectname,
            cloud_category=cloud_category,
            run_number=run_number,
            target_schema_name=target_schema_name,
            object_type=object_type
        )

        # Add the conversion task to background tasks
        background_tasks.add_task(run_conversion_background_task, request_data)

        # Return immediately while the conversion runs in background
        return {
            "message": "Conversion Agent Process Started Please Track the Conversion Agent Status logs",
            "status": "started",
            "project_id": project_id,
            "tgt_object_id": tgt_object_id,
            "migration_name": migration_name,
            "note": "The conversion process is running in the background. Monitor the server logs for progress updates."
        }

    except Exception as e:
        print(f"❌ Failed to start conversion workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to start conversion workflow",
                "message": str(e)
            }
        )


@router.post("/stage1-metadata-generation")
def stage1_metadata_generation(
    migration_name: str = Form(..., description="Name of the migration (e.g., Oracle_Postgres14)"),
    tgt_object_id: int = Form(..., description="Target object ID for the migration"),
    schema_name: str = Form(..., description="Schema name for the database object"),
    object_name: str = Form(..., description="Name of the object being migrated"),
    object_type: str = Form(..., description="Type of the object (e.g., procedure, function, view)"),
    cloud_category: str = Form(..., description="Deployment category: 'Local' or 'Cloud'"),
    run_no: str = Form(..., description="Run number for fileshare path"),
    source_code: str = Form(..., description="Original source database code"),
    target_code: str = Form(..., description="Target database code"),
    final_code: str = Form(..., description="Final reviewed and approved code by reviewer"),
    original_deployment_error: str = Form(..., description="Original deployment error message"),
    project_id: int = Form(..., description="Project ID for database connection")
):
    """
    Generate Stage 1 metadata files for Stage 2 processing.

    Creates directory structure based on cloud_category:
    - Local: {Config.Qbook_Local_Path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/
    - Cloud: {Config.Qbook_Path}/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/

    Files created in this directory:
    - approved_statements.csv with specified columns
    - source_code.sql file
    - target_code.sql file
    - final_code.sql file (reviewed and approved code)
    - Conversion_Files.xlsx (copied from PRJ{project_id}SRC/{run_no}/Source/{schema_name}/)
    - All files ending with _Package_Extraction.csv (copied from PRJ{project_id}SRC/{run_no}/Source/{schema_name}/)

    Approved statements are retrieved from the database using project_id for authentication
    and tgt_object_id to filter statements where review_status=true and is_manually_deployed=true.

    CSV includes: migration_name, schema_name, object_name, object_type, tgt_object_id,
    source_statement_number, target_statement_number, original_source_statement,
    original_target_statement, ai_converted_statement, original_deployment_error
    """
    try:
        print(f"🚀 Starting Stage 1 metadata generation...")
        print(f"📊 Migration: {migration_name}, Path: {object_type}/{schema_name}/{object_name}")

        # Get approved statements from database using the new function
        try:
            print(f"🔍 Retrieving approved statements from database for tgt_object_id: {tgt_object_id}")

            # Get project database credentials and establish connection (same pattern as conversion agent)
            # Get project database credentials using the same pattern as conversion agent
            print(f"🔐 Getting project database credentials for project_id: {project_id}")
            token_data = api_authentication(project_id)
            project_db_credentials = decrypt_database_details(token_data, project_id, 'Project', '')

            # Establish database connection
            connection = connect_database(project_db_credentials)
            print(f"🔌 Connected to project database successfully")

            try:
                db_statements = get_approved_statements_from_db(connection, tgt_object_id)
            finally:
                # Always close the database connection
                if connection:
                    connection.close()
                    print(f"🔌 Database connection closed")

            if not db_statements:
                raise HTTPException(
                    status_code=404,
                    detail={
                        "error": "No approved statements found",
                        "message": f"No approved statements found in database for tgt_object_id: {tgt_object_id}",
                        "suggestion": "Ensure statements are reviewed and manually deployed in the database"
                    }
                )

            print(f"📊 Retrieved {len(db_statements)} approved statements from database")

            # Convert database format to the expected list format for CSV generation
            approved_statements_list = []
            total_records = len(db_statements)

            for index, stmt in enumerate(db_statements):
                # Implement deployment error shifting logic:
                # 1st record: Use deployment_error from request
                # 2nd record: Use deployment_error from 1st database record
                # 3rd record: Use deployment_error from 2nd database record, etc.
                # Last record: Use deployment_error from previous record + append its own if exists

                if index == 0:
                    # First record uses deployment error from request
                    deployment_error_to_use = original_deployment_error
                else:
                    # Subsequent records use deployment error from previous database record
                    deployment_error_to_use = db_statements[index - 1]['deployment_error']

                # Special handling for last record: append its own deployment error if it exists
                if index == total_records - 1 and stmt['deployment_error'] and stmt['deployment_error'].strip():
                    # Last record and it has its own deployment error
                    if deployment_error_to_use and deployment_error_to_use.strip():
                        # Append to existing error with separator
                        deployment_error_to_use = deployment_error_to_use + "\n\nand\n\n" + stmt['deployment_error']
                    else:
                        # Use its own error if no previous error
                        deployment_error_to_use = stmt['deployment_error']

                statement_data = [
                    tgt_object_id,                    # tgt_object_id
                    stmt['source_statement_number'],  # source_statement_number
                    stmt['target_statement_id'],      # target_statement_number
                    stmt['source_statement'],         # original_source_statement
                    stmt['target_statement'],         # original_target_statement
                    stmt['ai_converted_statement'],   # ai_converted_statement
                    deployment_error_to_use           # deployment_error (shifted logic with last record handling)
                ]
                approved_statements_list.append(statement_data)

            print(f"✅ Successfully processed {len(approved_statements_list)} statements from database")
            print(f"📝 Using original_deployment_error from request: {original_deployment_error[:100]}...")

        except HTTPException:
            raise  # Re-raise HTTP exceptions as-is
        except Exception as db_error:
            print(f"❌ Error retrieving approved statements from database: {str(db_error)}")
            raise HTTPException(
                status_code=500,
                detail={
                    "error": "Database error",
                    "message": f"Failed to retrieve approved statements from database: {str(db_error)}",
                    "tgt_object_id": tgt_object_id
                }
            )

        # Create Stage 1 metadata directory structure
        directory_path = create_stage1_metadata_directory_structure(
            migration_name,
            schema_name,
            object_type,
            object_name,
            cloud_category
        )

        # Save approved statements CSV
        csv_file_path = save_approved_statements_csv(
            directory_path,
            migration_name,
            schema_name,
            object_name,
            object_type,
            approved_statements_list
        )

        # Save source, target, and final code files
        source_file_path, target_file_path, final_file_path = save_source_target_final_files(
            directory_path,
            source_code,
            target_code,
            final_code
        )

        # Copy files from project fileshare to migration_name/schema_name level
        schema_level_directory = os.path.join(
            Config.Qbook_Local_Path if cloud_category.lower() == "local" else Config.Qbook_Path,
            "Stage1_Metadata",
            migration_name,
            schema_name
        )
        os.makedirs(schema_level_directory, exist_ok=True)

        # Copy project files (if they exist, no error if not found)
        conversion_files_path = copy_project_file(schema_level_directory, run_no, schema_name, cloud_category, project_id, "Conversion_Files.xlsx")
        package_extraction_files = copy_files_by_pattern(schema_level_directory, run_no, schema_name, cloud_category, project_id, "_Package_Extraction.csv")

        print("🎉 Stage 1 metadata generation completed!")

        # Return success response
        response = {
            "message": "Stage 1 Metadata Generation Completed Successfully",
            "status": "completed",
            "migration_name": migration_name,
            "schema_name": schema_name,
            "object_name": object_name,
            "object_type": object_type,
            "cloud_category": cloud_category,
            "run_no": run_no,
            "tgt_object_id": tgt_object_id,
            "stage1_metadata_directory": directory_path,
            "files_created": {
                "approved_statements_csv": csv_file_path,
                "source_code_sql": source_file_path,
                "target_code_sql": target_file_path,
                "final_code_sql": final_file_path,
                "conversion_files_xlsx": conversion_files_path,
                "package_extraction_files": package_extraction_files
            },
            "total_approved_statements": len(approved_statements_list)
        }

        return response

    except HTTPException:
        # Re-raise HTTP exceptions (like JSON decode errors)
        raise
    except Exception as e:
        print(f"❌ Failed to generate Stage 1 metadata: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to generate Stage 1 metadata",
                "message": str(e)
            }
        )